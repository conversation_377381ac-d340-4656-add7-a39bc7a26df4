// Requires Sass 3.1+
@mixin radial-gradient($G1,        $G2,
                       $G3: null, $G4: null,
                       $G5: null, $G6: null,
                       $G7: null, $G8: null,
                       $G9: null, $G10: null,
                       $pos: null,
                       $shape-size: null,
                       $fallback: null) {

  $data: _radial-arg-parser($G1, $G2, $pos, $shape-size);
  $G1:  nth($data, 1);
  $G2:  nth($data, 2);
  $pos: nth($data, 3);
  $shape-size: nth($data, 4);

  $full: $G1, $G2, $G3, $G4, $G5, $G6, $G7, $G8, $G9, $G10;

  // Strip deprecated cover/contain for spec
  $shape-size-spec: _shape-size-stripper($shape-size);

  // Set $G1 as the default fallback color
  $first-color: nth($full, 1);
  $fallback-color: nth($first-color, 1);

  @if (type-of($fallback) == color) or ($fallback == "transparent") {
    $fallback-color: $fallback;
  }

  // Add Commas and spaces
  $shape-size: if($shape-size, '#{$shape-size}, ', null);
  $pos:        if($pos, '#{$pos}, ', null);
  $pos-spec:   if($pos, 'at #{$pos}', null);
  $shape-size-spec: if(($shape-size-spec != ' ') and ($pos == null), '#{$shape-size-spec}, ', '#{$shape-size-spec} ');

  background-color:  $fallback-color;
  background-image: -webkit-radial-gradient(unquote(#{$pos}#{$shape-size}#{$full}));
  background-image: unquote("radial-gradient(#{$shape-size-spec}#{$pos-spec}#{$full})");
}

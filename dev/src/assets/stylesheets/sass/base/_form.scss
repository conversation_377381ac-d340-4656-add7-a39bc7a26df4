// IOS OVERRIDE
// -----------------------------------------------------------------------------
/* Form - iOS Override */
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
textarea {
	-webkit-appearance: none;
}

// PLACEHOLDERS
// -----------------------------------------------------------------------------
.form-control,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
textarea {
	@include placeholder-color(#bdbdbd);
}

.form-control:focus {
  border-color: $form-control-border-color;
  outline: 0;
  -webkit-box-shadow: $form-control-box-shadow;
  box-shadow: $form-control-box-shadow;
}

// DARK - FORM
// -----------------------------------------------------------------------------
html.dark {
	.form-control {
		background-color: $dark-color-3;
		border-color: $dark-color-3;
		color: #EEE;
	}

	.form-control[disabled],
	.form-control[readonly],
	fieldset[disabled] .form-control {
		background-color: $dark-color-2;
	}

	.input-group-addon {
		background-color: $dark-color-2;
		border-color: $dark-color-2;
		color: #EEE;
	}
}

// BOOTSTRAP OVERRIDE
// -----------------------------------------------------------------------------
/* Form - Bootstrap Override */
.btn-lg,
.btn-group-lg > .btn {
	line-height: 1.334;
}

select {
	&.input-sm,
	&.input-lg {
		line-height: 1;
	}
}

.bootstrap-timepicker-widget {
	input {
		border: 0;
	}
}

// CUSTOM FIELDS
// -----------------------------------------------------------------------------
/* Form - Custom Fields */
.required {
	display: inline-block;
	color: $color-danger;
	font-size: 0.8em;
	font-weight: bold;
	position: relative;
	top: -0.2em;
}

label.error {
	color: #B94A48;
	margin-top: 2px;
}

// FORM GROUP (OVERRIDE)
// -----------------------------------------------------------------------------
/* Form - Group Override */
.form-group {
	&:after {
		clear: both;
		display: block;
		content: '';
	}

	&:last-child,
	&:last-of-type {
		margin-bottom: 0;
	}
}

// BORDERED FORM
// -----------------------------------------------------------------------------
/* Form - Bordered */
.form-bordered {
	.form-group {
		border-bottom: 1px solid #eff2f7;
		padding-bottom: 15px;
		margin-bottom: 15px;

		&:last-child,
		&:last-of-type {
			border-bottom: none !important;
			padding-bottom: 0px !important;
			margin-bottom: 0px !important;
		}
	}

}

// DARK - BORDERED FORM
// -----------------------------------------------------------------------------
/* Dark - Form - Bordered */
html.dark {
	.form-bordered {
		.form-group {
			border-bottom: 1px solid darken($dark-color-4, 5%);
			padding-bottom: 15px;
			margin-bottom: 15px;
		}
	}
}


// VERTICAL GROUP / STACKED
// -----------------------------------------------------------------------------
/* Form - Vertical Group / Stacked */
.form-group-vertical {
	position: relative;
	white-space: nowrap;

	.form-control {
		border-radius: 0;
		margin-top: -1px;
		z-index: 1;

		&:first-child,
		&:first-of-type {
			border-radius: 4px 4px 0 0;
		}

		&:last-child,
		&:last-of-type {
			border-radius: 0 0 4px 4px;
		}

		&:focus {
			position: relative;
			z-index: 2;
		}
	}

	.input-group {
		margin-top: -1px;

		.form-control {
			margin-top: 0;
		}

		&:first-child,
		&:first-of-type {
			.input-group-addon {
				border-radius: 4px 0 0 0;
			}

			.form-control {
				border-radius: 0 4px 0 0;
			}
		}

		&:last-child,
		&:last-of-type {
			.input-group-addon {
				border-radius: 0 0 0 4px;
			}

			.form-control {
				border-radius: 0 0 4px 0;
			}
		}

		&.input-group-icon {

			&:first-child,
			&:first-of-type {
				.input-group-addon {
					border-radius: 4px 4px 0 0;
				}

				.form-control {
					border-radius: 4px 4px 0 0;
				}
			}

			&:last-child,
			&:last-of-type {
				.input-group-addon {
					border-radius: 0 0 4px 4px;
				}

				.form-control {
					border-radius: 0 0 4px 4px;
				}
			}

		}
	}

}

// INPUT (OVERRIDE)
// -----------------------------------------------------------------------------
/* Form - Input Override */
.input-lg {
	border-radius: 4px;
}

// INPUT ICON
// -----------------------------------------------------------------------------
/* Form - Input Icon */
.input-group-icon,
.input-search {
	width: 100%;
	table-layout: fixed;

	input.form-control {
		@include font-size(12);
		padding-right: 36px;

		&:first-child,
		&:last-child {
			border-radius: 4px;
		}
	}

	.input-group-btn {
		border-radius: 500px;
		width: 0;

		&:first-child,
		&:last-child {
			border-radius: 500px;
		}

		button {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			border: 0;
			z-index: 3;
			background:transparent;

			&:active {
				-webkit-box-shadow: none;
				box-shadow: none;
			}
		}

		&:last-child {
			button {
				left: auto;
				right: 0;
			}
		}

		& + input.form-control {
			padding-right: 12px;
			padding-left: 36px;
		}
	}

	.input-group-addon {
		position: relative;
		padding: 0;
		border: 0 none;
		width: 0;

		span.icon {
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			border: 0;
			z-index: 3;
			width: auto;
			display: inline-block;
			vertical-align: middle;
			text-align: center;
			padding: 6px 12px;
			background: transparent;
			line-height: 1.42857143;
			-webkit-box-sizing: content-box;
			-moz-box-sizing: content-box;
			box-sizing: content-box;
			pointer-events: none;

			&.icon-lg {
				padding: 10px 14px;
				font-size: 18px;
			}
		}

		&:last-child {
			span.icon {
				left: auto;
				right: 0;
			}
		}

		& + input.form-control {
			padding-right: 12px;
			padding-left: 36px;
		}
	}

}

// INPUT SEARCH
// -----------------------------------------------------------------------------
/* Form - Input Search */
.input-search {
	width: 100%;

	input.form-control {
		&:focus {
			border-color: #ccc;
			-webkit-box-shadow: none;
			box-shadow: none;
		}
	}

	.input-group-btn {
		color: #ccc;

		.btn {
			padding-left: 15px;
		}

		.btn-default {
			color: #ccc;
		}
	}
}

/* Dark */
.input-search {
	width: 100%;

	input.form-control {
		&:focus {
			border-color: $dark-color-1;
		}
	}

	.input-group-btn {
		.btn {
			background: transparent !important;
		}
	}
}

// ROUND INPUT
// -----------------------------------------------------------------------------
/* Form - Round Input */
input.input-rounded {
	-webkit-border-radius: 500px;
	border-radius: 500px;
}

.input-group-rounded,
.input-search {
	input.form-control {
		-webkit-border-radius: 500px;
		border-radius: 500px;

		&:first-child,
		&:last-child {
			border-radius: 500px;
		}
	}

	.input-group-addon {

		&:first-child {
			border-radius: 500px 0 0 500px;
		}

		&:last-child {
			border-radius: 0 500px 500px 0;
		}
	}
}

// CUSTOM CHECKBOX
// -----------------------------------------------------------------------------
/* Form - Custom Checkbox */
.checkbox-custom {
	position: relative;
	padding: 0 0 0 25px;
	margin-bottom: 7px;
	margin-top: 0;

	&.checkbox-inline {
		display: inline-block;
		vertical-align: middle;

		.form-group & {
			margin-top: 7px;
			padding-top: 0;
		}
	}

	&:last-child,
	&:last-of-type {
		margin-bottom: 0;
	}

	input[type="checkbox"] {
		opacity: 0;
		position: absolute;
		top: 50%;
		left: 3px;
		margin: -6px 0 0 0;
		z-index: 2;
		cursor: pointer;

		&:checked {
			& + label:after {
				position: absolute;
				display: inline-block;
				font-family: 'FontAwesome';
				content: '\F00C';
				top: 50%;
				left: 4px;
				margin-top: -5px;
				font-size: 11px;
				line-height: 1;
				width: 16px;
				height: 16px;
				color: #333;
			}
		}

		&:disabled {
			cursor: not-allowed;

			&:checked + label:after {
				color: #999;
			}

			& + label {
				cursor: not-allowed;
			}

			& + label:before {
				background-color: #eee;
			}
		}
	}

	label {
		cursor: pointer;
		margin-bottom: 0;
		text-align: left;
		line-height: 1.2;

		&:before {
			content: '';
			position: absolute;
			top: 50%;
			left: 0;
			margin-top: -9px;
			width: 19px;
			height: 18px;
			display: inline-block;
			border-radius: 2px;
			border: 1px solid #bbb;
			background: #fff;
		}

		& + label.error {
			display: block;
		}
	}

}

html.dark {
	.checkbox-custom {
		label {
			&:before {
				background: $dark-color-3;
				border-color: $dark-color-2;
			}
		}
		input[type="checkbox"] {
			&:checked + label:after {
				color: #fff;
			}
			&:disabled + label:before {
				background: $dark-color-5;
				border-color: $dark-color-5;
			}
		}
	}
}

@each $state in $states {
	html.dark .checkbox-#{nth($state,1)},
	.checkbox-#{nth($state,1)} {

		input[type="checkbox"]:checked + label:after {
			color: #fff;
		}

		label:before {
			background: #{nth($state,2)};
			border-color: darken( nth($state,2), 5% );
		}
	}

	html.dark .checkbox-text-#{nth($state,1)},
	.checkbox-text-#{nth($state,1)} {
		input[type="checkbox"]:checked + label:after {
			color: #{nth($state, 2)};
		}
	}
}

// CUSTOM RADIO
// -----------------------------------------------------------------------------
/* Form - Custom Radio */
.radio-custom {
	position: relative;
	padding: 0 0 0 25px;
	margin-bottom: 7px;
	margin-top: 0;

	&.radio-inline {
		display: inline-block;
		vertical-align: middle;

		.form-group & {
			margin-top: 7px;
			padding-top: 0;
		}
	}

	&:last-child,
	&:last-of-type {
		margin-bottom: 0;
	}

	input[type="radio"] {
		opacity: 0;
		position: absolute;
		top: 50%;
		left: 3px;
		margin: -6px 0 0 0;
		z-index: 2;
		cursor: pointer;

		&:checked {
			& + label:after {
				content: '';
				position: absolute;
				top: 50%;
				left: 4px;
				margin-top: -5px;
				display: inline-block;
				font-size: 11px;
				line-height: 1;
				width: 10px;
				height: 10px;
				background-color: #444;
				border-radius: 50px;
				-webkit-box-shadow: 0px 0px 1px #444;
				box-shadow: 0px 0px 1px #444;
			}
		}

		&:disabled {
			cursor: not-allowed;

			&:checked + label:after {
				color: #999;
			}

			& + label {
				cursor: not-allowed;
			}

			& + label:before {
				background-color: #eee;
			}
		}
	}

	label {
		cursor: pointer;
		margin-bottom: 0;
		text-align: left;
		line-height: 1.2;

		&:before {
			content: '';
			position: absolute;
			top: 50%;
			left: 0;
			margin-top: -9px;
			width: 18px;
			height: 18px;
			display: inline-block;
			border-radius: 50px;
			border: 1px solid #bbb;
			background: #fff;
		}

		& + label.error {
			display: block;
		}
	}

}

html.dark {
	.radio-custom {
		label {
			&:before {
				background: $dark-color-3;
				border-color: $dark-color-2;
			}
		}
		input[type="radio"]{
			&:checked + label:after {
				background-color: #fff;
			}
			&:disabled + label:before {
				background: $dark-color-5;
				border-color: $dark-color-5;
			}
		}
	}
}

@each $state in $states {
	html.dark .radio-#{nth($state,1)},
	.radio-#{nth($state,1)} {

		input[type="radio"]:checked + label:after {
			background: #{nth($state,2)};
			-webkit-box-shadow: 0px 0px 1px #{nth($state,2)};
			box-shadow: 0px 0px 1px #{nth($state,2)};
		}

	}
}

// ERROR CONTAINER
// -----------------------------------------------------------------------------
/* Form - Error Container */
div.validation-message ul {
	display: none;
	list-style: none;
	margin: -15px -15px 15px -15px;
	padding: 15px;
	border-bottom: 1px solid #FFCBCB;
	background: #FFEFEF;

	label.error {
		display: block;
		padding-left: 22px;
		position: relative;

		&:before {
			font-family: 'FontAwesome';
			content: '\f00d';
			position: absolute;
			top: 0;
			left: 0;
			font-size: 16px;
			color: #D9534F;
			display: inline-block;
		}
	}
}

// SELECT2 FIX
// -----------------------------------------------------------------------------
.select2-drop-mask {
    z-index: 10010;
}

.select2-drop {
    z-index: 10011;
}

.select2-search {
    z-index: 10012;
}

.select2-container--bootstrap.select2-container--open {
	z-index: 10013;
}

@media (max-width: 991px) {
	.select2-container--bootstrap {
		width: auto !important;
	}
}

// MAXLENGTH FIX
// -----------------------------------------------------------------------------
.bootstrap-maxlength {
	z-index: 999999 !important;
}

// DARK - FILE UPLOAD
// -----------------------------------------------------------------------------
html.dark {
	.fileupload .uneditable-input {
		background-color: $dark-color-3;
		border-color: $dark-color-3;
	}

	.fileupload-new .input-append .btn-file {
		border-color: $dark-color-2;
	}
}
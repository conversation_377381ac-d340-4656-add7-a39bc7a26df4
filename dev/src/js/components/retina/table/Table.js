import React from 'react';
import PropTypes from 'prop-types';
import { Table as MajooUiTable } from '@majoo-ui/react';

const Table = ({
    columns,
    data,
    pageIndex,
    rowLimit,
    totalData,
    hasMoreItems,
    fetchData,
    isLoading,
    searchQuery,
    title,
    showSearch = true,
    showPagination = true,
    onRowClick,
    ...tableProps
}) => (
    <MajooUiTable
        columns={columns}
        data={data}
        pageIndex={pageIndex}
        rowLimit={rowLimit}
        totalData={totalData}
        hasMoreItems={hasMoreItems}
        fetchData={fetchData}
        isLoading={isLoading}
        searchQuery={searchQuery}
        title={title}
        showSearch={showSearch}
        showPagination={showPagination}
        onRowClick={onRowClick}
        css={{
            '& th': {
                height: '48px',
            },
            '& td': {
                height: '48px',
                paddingTop: '$spacing-05',
                paddingBottom: '$spacing-05',
            },
        }}
        {...tableProps}
    />
);

Table.propTypes = {
    columns: PropTypes.arrayOf(
        PropTypes.shape({
            Header: PropTypes.string.isRequired,
            accessor: PropTypes.string.isRequired,
        }),
    ).isRequired,
    data: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
    pageIndex: PropTypes.number.isRequired,
    rowLimit: PropTypes.number.isRequired,
    totalData: PropTypes.number.isRequired,
    hasMoreItems: PropTypes.bool,
    fetchData: PropTypes.func.isRequired,
    isLoading: PropTypes.bool,
    searchQuery: PropTypes.string,
    title: PropTypes.string,
    showSearch: PropTypes.bool,
    showPagination: PropTypes.bool,
    onRowClick: PropTypes.func,
};

Table.defaultProps = {
    hasMoreItems: false,
    isLoading: false,
    searchQuery: '',
    title: '',
    showSearch: true,
    showPagination: true,
    onRowClick: () => {},
};

export default Table;

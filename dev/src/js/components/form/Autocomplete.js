/**
 * Created by <PERSON><PERSON> on 17/01/2017.
 */
import React from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import Option from './Option';

class Autocomplete extends React.Component {
  constructor(props) {
    super(props);
    const { data, selector, resultPerpage } = this.props;
    this.state = {
      optionsList: data,
      currentList: [],
      page: 1,
      resultPerpage,
      selector,
      currentId: '',
      currentValue: '',
      showDropDown: false,
      activeDropDown: false,
    };

    this.showDropDown = this.showDropDown.bind(this);
    this.hideDropDown = this.hideDropDown.bind(this);
    this.activeDropDown = this.activeDropDown.bind(this);
    this.inactiveDropDown = this.inactiveDropDown.bind(this);
    this.changeValueHandle = this.changeValueHandle.bind(this);
    this.pickValueHandle = this.pickValueHandle.bind(this);
    this.resetValue = this.resetValue.bind(this);
    this.onScroll = this.onScroll.bind(this);
  }

  componentDidMount() {
    window.addEventListener('scroll', this.onScroll, true);
  }

  componentWillReceiveProps(nextProps) {
    const { value } = this.props;
    const { optionsList, currentList } = this.state;
    if (nextProps.data !== optionsList) {
      const newOptionsList = update(currentList, {
        $push: nextProps.data,
      });
      this.setState({
        optionsList: nextProps.data,
        currentList: newOptionsList,
      });
    }
    if (nextProps.value !== value) {
      if (nextProps.value !== '' && nextProps.value !== undefined) {
        nextProps.data.forEach((data) => {
          if (data.id === nextProps.value) {
            const displayValue = nextProps.selector && data[nextProps.selector] ? data[nextProps.selector] : (data.name || data);
            this.setState({
              currentId: data.id,
              currentValue: displayValue,
              showDropDown: false,
              activeDropDown: false,
            });
          }
        });
      } else {
        this.setState({
          currentId: '',
          currentValue: '',
          showDropDown: false,
          activeDropDown: false,
        });
      }
    } else if (nextProps.value !== '' && nextProps.value !== undefined) {
      nextProps.data.forEach((data) => {
        if (data.id === nextProps.value) {
          const displayValue = nextProps.selector && data[nextProps.selector] ? data[nextProps.selector] : (data.name || data);
          this.setState({
            currentId: data.id,
            currentValue: displayValue,
            showDropDown: false,
            activeDropDown: false,
          });
        }
      });
    }
  }

  componentWillUnmount() {
    window.removeEventListener('scroll', this.onScroll);
  }

  onScroll() {
    const { activeDropDown, page } = this.state;
    const { changeScroll } = this.props;
    if (activeDropDown === true) {
      if (this.iScroll.scrollTop + this.iScroll.clientHeight >= this.iScroll.scrollHeight) {
        this.setState({
          page: page + 1,
          showDropDown: true,
        }, () => {
          changeScroll(page);
        });
      }
    }
  }

  showDropDown() {
    const { optionsList } = this.state;
    this.setState({
      showDropDown: true,
      page: 1,
      currentList: optionsList,
    });
  }

  hideDropDown() {
    const { optionsList } = this.state;
    this.setState({
      showDropDown: false,
      page: 1,
      currentList: optionsList,
    });
  }

  activeDropDown() {
    this.setState({ activeDropDown: true });
  }

  inactiveDropDown() {
    this.setState({ activeDropDown: false });
  }

  changeValueHandle(event) {
    const { currentId } = this.state;
    const { changeEvent, onSearch } = this.props;
    if (currentId === '') {
      const { value } = event.target;
      this.showDropDown();
      this.setState({
        currentValue: value,
      }, () => {
        const { currentValue } = this.state;
        if (onSearch) onSearch(currentValue);
        changeEvent(currentId, currentValue);
      });
    }
  }

  pickValueHandle(id, value) {
    this.setState({
      currentId: id,
      currentValue: value,
      showDropDown: false,
      activeDropDown: false,
    }, () => {
      const { currentId, currentValue } = this.state;
      const { changeEvent } = this.props;
      changeEvent(currentId, currentValue);
    });
  }

  resetValue() {
    const { changeEvent, onSearch } = this.props;
    this.setState({ currentId: '', currentValue: '' }, () => {
      const { currentId, currentValue } = this.state;
      changeEvent(currentId, currentValue, { isReset: true });
      if (onSearch) onSearch('');
    });
  }

  renderDropdown() {
    const {
      optionsList, currentValue, selector, currentList,
    } = this.state;
    const { isPaginate } = this.props;
    const list = isPaginate ? currentList : optionsList;
    return (
      list.map((item, index) => {
        // Handle case when selector is not provided - use 'name' as default or the item itself if it's a string
        const displayValue = selector && item[selector] ? item[selector] : (item.name || item);
        const itemValue = selector && item[selector] ? item[selector] : (item.name || item);

        if (String(displayValue).toLowerCase().includes(String(currentValue).toLowerCase())) {
          const charIndex = String(displayValue).toLowerCase().indexOf(String(currentValue).toLowerCase());
          const { length } = currentValue;
          return (
            <Option key={index} activeEvent={this.activeDropDown} clickEvent={this.pickValueHandle} preventer={this.showDropDown} value={itemValue} id={item.id}>
              {String(displayValue).substring(0, charIndex)}
              <b style={{ color: '#fccb36' }}>{String(displayValue).substring(charIndex, charIndex + length)}</b>
              {String(displayValue).substring(charIndex + length, String(displayValue).length)}
            </Option>
          );
        }
        return true;
      })
    );
  }

  render() {
    const {
      placeholder, disabled, classes, label, name, required, autoComplete, dropdownStyle,
    } = this.props;
    const {
      showDropDown, activeDropDown, currentValue,
    } = this.state;
    return (
      <div>
        {label && <label className="control-label">{label}</label>}
        {!disabled
        && (
          <div className="autocomplete">
            <input
                type="text"
                className="form-control autocomplete-input"
                placeholder={placeholder}
                onFocus={this.showDropDown}
                onBlur={this.hideDropDown}
                onChange={this.changeValueHandle}
                value={currentValue}
                name={name}
                required={required}
                autoComplete={autoComplete}
            />
            <div ref={(el) => { this.iScroll = el; }} onMouseOver={this.activeDropDown} onMouseOut={this.inactiveDropDown} onFocus={this.showDropDown} onBlur={this.hideDropDown} className={`autocomplete-dropdown${(showDropDown || activeDropDown) ? '' : ' hidden'}`} style={{ zIndex: 11, ...dropdownStyle }}>
              {this.renderDropdown()}
            </div>
            {currentValue === '' && <div className="reset-value"><i className="fa fa-search" /></div>}
            {(currentValue !== '') && <span className="reset-value" onClick={this.resetValue} role="presentation"><i className="fa fa-close" /></span>}
          </div>
          )
        }

        {disabled
        && (
          <div className="form-group">
            <input
              type="text"
              className={`${classes} form-control`}
              value={currentValue}
              disabled
              placeholder={placeholder}
              name={name}
              required={required}
            />
          </div>
          )
        }
      </div>
    );
  }
}

Autocomplete.propTypes = {
    data: PropTypes.instanceOf(Array),
    selector: PropTypes.string,
    value: PropTypes.string,
    changeEvent: PropTypes.func,
    changeScroll: PropTypes.func,
    onSearch: PropTypes.func,
    disabled: PropTypes.bool,
    classes: PropTypes.string,
    placeholder: PropTypes.string,
    label: PropTypes.string,
    isPaginate: PropTypes.bool,
    name: PropTypes.string,
    required: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.bool,
    ]),
    autoComplete: PropTypes.string,
    dropdownStyle: PropTypes.shape({ }),
};

Autocomplete.defaultProps = {
    data: [],
    selector: '',
    value: '',
    changeEvent: () => {
    },
    changeScroll: () => {
    },
    onSearch: () => {
    },
    disabled: false,
    classes: '',
    placeholder: '',
    label: '',
    isPaginate: false,
    name: '',
    required: '',
    autoComplete: 'off',
    dropdownStyle: undefined,
};

export default Autocomplete;

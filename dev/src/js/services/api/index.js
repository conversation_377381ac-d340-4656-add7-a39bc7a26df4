
import * as sessionSelectors from '../session/selectors';
import {
    API_BASE_SANTAN, API_BASE_PORTAL, API_BASE_BILLER, API_BASE_NOTIFICATION, API_BASE_SHIPPING, API_BASE_MENU_UTILITIES, API_BASE_MULANG,
    API_BASE_PAYROLL, API_BASE_MAYANG,
} from '../../config/config';
import { setLoader } from './setLoader';

let jumlahHit = 0;

const getQueryString = (params) => {
    const esc = encodeURIComponent;
    return Object.keys(params)
        .map((k) => {
            if (!Array.isArray(params[k])) return `${esc(k)}=${esc(params[k])}`;
            return params[k].map(p => `${esc(k)}=${esc(p)}`).join('&');
        })
        .join('&');
};

const generateRequestHeaderAuthorization = (authType = 'token', customToken = '') => {
    let authorization = {};
    const token = customToken || sessionSelectors.getToken();
    if (!token) return authorization;
    switch (authType) {
        case 'bearer':
            authorization = { Authorization: `Bearer ${token}` };
            break;
        default:
            authorization = { Token: token };
            break;
    }
    return authorization;
};

export const fetchApi = async (
    endPoint,
    payload = {},
    method = 'get',
    options = {
        serviceDomainType: undefined,
        authType: undefined,
        customToken: undefined,
        slashId: undefined,
        queryString: undefined,
        isEnableLoader: true,
    },
) => {
    jumlahHit += 1;
    const clonedOptions = { ...options };
    if (clonedOptions.isEnableLoader) {
        setLoader(true);
    }
    let qs = '';
    let body;
    let slashId = '';

    if (['get', 'head'].indexOf(method) > -1) {
        if (payload && Object.keys(payload).length > 0) qs = `?${getQueryString(payload)}`;
    } else if (['delete'].indexOf(method) > -1) {
        Object.keys(payload).forEach((key) => {
            if (body === undefined) {
                body = `${key}=${encodeURIComponent(payload[key])}`;
            } else {
                body = `${body}&&${key}=${encodeURIComponent(payload[key])}`;
            }
        });
        // body = `${Object.keys(payload)[0]}=${encodeURIComponent(Object.values(payload)[0])}`;
    } else if (['patch'].indexOf(method) > -1) {
        body = payload;
    } else {
        body = JSON.stringify(payload);
    }

    if (clonedOptions.queryString) {
        qs = `?${getQueryString(clonedOptions.queryString)}`;
    }

    let HOST_COMMON = '';
    let HOST_PORTAL = API_BASE_PORTAL;
    let HOST_MAYANG = API_BASE_MAYANG;
    let HOST_BILLER = API_BASE_BILLER;

    if (process.env.APP_ENV !== 'development') {
        HOST_COMMON = process.env.SERVICE_BASE_COMMON;
        HOST_PORTAL = `${process.env.SERVICE_BASE_PORTAL}/`;
        HOST_MAYANG = `${process.env.SERVICE_BASE_MAYANG}/`;
        HOST_BILLER = `${process.env.SERVICE_BASE_BILLER}/`;
    }

    let apiBase = `${HOST_COMMON}${API_BASE_SANTAN}`;
    let token;

    if (clonedOptions.serviceDomainType) {
        if (clonedOptions.serviceDomainType === 'portal') {
            token = 'aHR0cHM6Ly9tYWpvby5pZA.YVV0SHRPQXBJ.0fMAGQ1_g9DXeX1CWr1kLqZlsQYg4dO3ankutzCTM2w';
            apiBase = HOST_PORTAL;
        } else if (clonedOptions.serviceDomainType === 'biller') {
            apiBase = HOST_BILLER;
        } else if (clonedOptions.serviceDomainType === 'notification') {
            apiBase = `${HOST_COMMON}${API_BASE_NOTIFICATION}`;
        } else if (clonedOptions.serviceDomainType === 'shipping') {
            apiBase = `${HOST_COMMON}${API_BASE_SHIPPING}`;
        } else if (clonedOptions.serviceDomainType === 'menuUtilities') {
            apiBase = `${HOST_COMMON}${API_BASE_MENU_UTILITIES}`;
        } else if (clonedOptions.serviceDomainType === 'mulang') {
            apiBase = `${HOST_COMMON}${API_BASE_MULANG}`;
        } else if (clonedOptions.serviceDomainType === 'payroll') {
            apiBase = `${HOST_COMMON}${API_BASE_PAYROLL}`;
        } else if (clonedOptions.serviceDomainType === 'mayang') {
            apiBase = HOST_MAYANG;
        }
    }

    if (clonedOptions.slashId) {
        slashId = `/${clonedOptions.slashId}`;
    }

    const url = `${apiBase}${endPoint}${slashId}${qs}`;
    const mergedHeaders = {
        ...generateRequestHeaderAuthorization(clonedOptions.authType, token),
        'Content-Type': String(method).toLowerCase() === 'delete' ? 'application/x-www-form-urlencoded' : 'application/json',
    };

    const response = await fetch(url, {
        method: method.toLowerCase(),
        headers: mergedHeaders,
        body,
    });

    if (response) {
        jumlahHit -= 1;
    }

    if (jumlahHit === 0) {
        if (clonedOptions.isEnableLoader) {
            setLoader(false);
        }
        jumlahHit = 0;
    }

  if (!response.ok) {
    let errMsg = response.statusText;
    const responseBody = await response.json();

    if (responseBody) {
        if (typeof responseBody === 'string') {
            errMsg = responseBody;
        }

        if (typeof responseBody === 'object') {
            if (Object.hasOwnProperty.call(responseBody, 'msg')) {
                errMsg = responseBody.msg;
            }
            if (Object.hasOwnProperty.call(responseBody, 'message')) {
                errMsg = responseBody.message;
            }
            if (Object.hasOwnProperty.call(responseBody, 'status') && typeof responseBody.status === 'object' && Object.hasOwnProperty.call(responseBody.status, 'message')) {
                errMsg = responseBody.status.message;
            }
        }
    }

    throw Error(errMsg);
  }

  let data = null;
  try {
    data = await response.json();
  } catch (e) {}
  return data;
};

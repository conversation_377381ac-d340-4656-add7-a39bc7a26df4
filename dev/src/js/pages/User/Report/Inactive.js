import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';
import _ from 'lodash';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/retina/table/Table';
import InputText from '../../../components/form/InputText';
import { getuserInactive } from '../../../data/users/profile';
import SidePopupDetailOutlet from '../../../components/sidepopup/SidePopupDetailOutlet';
import Select from '../../../components/form/Select';
import { inactiveUserTable } from './tableMeta';
import { subscriptionList } from './enum';
import { catchError } from '../../../utils/helper';

const DEFAULT_START_DATE = moment().startOf('month').format('DD/MM/YYYY');
const DEFAULT_END_DATE = moment().endOf('month').format('DD/MM/YYYY');

class InactiveUser extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
  }

  static defaultProps = {
    calendar: { start: '', end: '' },
    assignCalendar: null,
    assignButtons: null,
    notificationSystem: ({ addNotification: () => { } }),
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
  }

  constructor(props) {
    super(props);

    this.debouncedSearch = _.debounce(this.handleSearchChange, 500);

    this.state = {
      idCabang: '',
      desc: '',
      assignFilterColoumnValue: 'Column',
      filterSubscription: '0',
      searchQuery: '',
      dateFilter: {
        start: DEFAULT_START_DATE,
        end: DEFAULT_END_DATE,
      },
      tableData: [],
      tableMeta: {
        pageIndex: 1,
        limit: 10,
        totalData: 0,
        hasMoreItems: true,
      },
      tableLoading: false,
    };
  }

  componentDidMount() {
    this.initiateRangeDate();
    this.initiateFilterColumn();
    const { assignCalendar, assignButtons } = this.props;

    assignCalendar(DEFAULT_START_DATE, DEFAULT_END_DATE, (startDate, endDate) => {
      this.changeCalendarHandler(startDate, endDate);
    });
    assignButtons([]);

    this._onFetchs({ pageIndex: 0 });
  }

  componentDidUpdate(prevProps, prevState) {
    const {
      filterSubscription, assignFilterColoumnValue, dateFilter, searchQuery,
    } = this.state;
    const filtersChanged = prevState.filterSubscription !== filterSubscription
      || prevState.assignFilterColoumnValue !== assignFilterColoumnValue
      || prevState.dateFilter !== dateFilter
      || prevState.searchQuery !== searchQuery;

    if (filtersChanged) {
      this._onFetchs({ pageIndex: 0 });
    }
  }

  _onFetchs = async (params) => {
    const { notificationSystem, showProgress, hideProgress } = this.props;
    const {
      assignFilterColoumnValue, dateFilter, filterSubscription, searchQuery, tableMeta,
    } = this.state;
    const {
      pageIndex, pageSize, sortAccessor, sortDirection,
    } = params;

    showProgress();
    this.setState({ tableLoading: true });

    let apiParams = {
      page: pageIndex + 1,
      limit: pageSize || 10,
      filterColumn: assignFilterColoumnValue,
      start: moment(dateFilter.start, 'DD/MM/YYYY').format('DD/MM/YYYY'),
      end: moment(dateFilter.end, 'DD/MM/YYYY').format('DD/MM/YYYY'),
      support: filterSubscription,
    };

    if (sortAccessor) {
      apiParams = { ...apiParams, column: sortAccessor, sort: sortDirection };
    }
    if (searchQuery) {
      apiParams = { ...apiParams, search: searchQuery };
    }

    try {
      const {
        desc, data, total_page: pageCount, total_row: totalData,
      } = await getuserInactive(apiParams);
      this.setState({
        desc,
        tableData: data,
        tableMeta: {
          ...tableMeta,
          pageIndex,
          totalData: Number(totalData),
          hasMoreItems: pageIndex + 1 < pageCount,
        },
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed to get data',
        message: catchError(err),
        level: 'error',
      });
      this.setState({ tableData: [], tableMeta: { ...tableMeta, totalData: 0, hasMoreItems: false } });
    } finally {
      this.setState({ tableLoading: false });
      hideProgress();
    }
  }

  initiateRangeDate = () => {
    const { assignRangeDate } = this.props;
    assignRangeDate([]);
  }

  initiateFilterColumn = () => {
    const { assignFilterColoumn } = this.props;
    const { assignFilterColoumnValue } = this.state;
    assignFilterColoumn([
      {
        option: ['Column', 'Last Trx', 'Outlet Register', 'Exp Date'],
        value: assignFilterColoumnValue,
        action: (value) => { this.updateFilterColoum(value); },
      },
    ]);
  }

  updateFilterColoum = (val) => {
    this.setState({
      assignFilterColoumnValue: val,
    }, this.initiateFilterColumn);
  }

  callEditDetailHandler = ({ original }) => {
    this.setState({
      idCabang: original.id_cabang,
    }, () => {
      this.detailSide.showPopup();
    });
  }

  changeCalendarHandler = (start, end) => {
    const { assignCalendar } = this.props;
    assignCalendar(start, end);
    this.setState({ dateFilter: { start, end } });
  }

  handleSearchChange = (value) => {
    this.setState({ searchQuery: value });
  }

  changeSubscriptionHandler = (val) => {
    this.setState({ filterSubscription: val });
  }

  render() {
    const {
      idCabang, desc, filterSubscription, tableData, tableMeta, tableLoading,
    } = this.state;

    return (
      <div>
        <section className="panel">
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-5">
                <h4 className="panel-title" style={{ margin: '8px' }}>
                  Inactive User
                  <br />
                  <small>
                    {desc}
                  </small>
                </h4>
              </div>
              <div className="col-md-4">
                <Select
                  data={subscriptionList}
                  value={filterSubscription}
                  changeEvent={this.changeSubscriptionHandler}
                />
              </div>
              <div className="col-md-3">
                <InputText
                  classes="filter"
                  placeholder="Cari..."
                  changeEvent={this.debouncedSearch}
                />
              </div>
            </div>
          </div>
          <div className="panel-body">
            <Table
              columns={inactiveUserTable}
              data={tableData}
              fetchData={this._onFetchs}
              isLoading={tableLoading}
              onRowClick={this.callEditDetailHandler}
              pageIndex={tableMeta.pageIndex}
              rowLimit={tableMeta.limit}
              totalData={tableMeta.totalData}
            />
          </div>
        </section>
        {idCabang !== '' && (
          <SidePopupDetailOutlet
            ref={(c) => { this.detailSide = c; }}
            id={idCabang}
            showMaps
            longDetail={60}
            parentProps={this.props}
          />
        )}
      </div>
    );
  }
}

export default CoreHOC(InactiveUser);

import React from 'react';
import PropTypes from 'prop-types';

const STATUS_TYPE = {
    WAITING: 'waiting',
    REJECT: 'reject',
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    DELETED: 'deleted',
};

const StatusColumn = ({ value }) => {
  let retval = (<div className="wallet-status-label in-progress">Waiting</div>);

  if (String(value) === STATUS_TYPE.WAITING) {
    retval = (<div className="wallet-status-label in-progress">Waiting</div>);
  } else if (String(value) === STATUS_TYPE.REJECT) {
    retval = (<div className="wallet-status-label rejected">Reject</div>);
  } else if (String(value) === STATUS_TYPE.ACTIVE) {
    retval = (<div className="wallet-status-label approved">Active</div>);
  } else if (String(value) === STATUS_TYPE.INACTIVE) {
    retval = (<div className="wallet-status-label rejected">Inactive</div>);
  } else if (String(value) === STATUS_TYPE.DELETED) {
    retval = (<div className="wallet-status-label rejected">Deleted</div>);
  }

  return retval;
};

StatusColumn.propTypes = {
  value: PropTypes.string,
};

StatusColumn.defaultProps = {
  value: '',
};

export default StatusColumn;

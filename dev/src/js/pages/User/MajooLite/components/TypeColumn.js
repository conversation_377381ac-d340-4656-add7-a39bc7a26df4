import PropTypes from 'prop-types';

const TYPE_VALUE = {
    BANK_TRANSFER: 'bank_transfer',
    QRIS: 'qris',
};

const TypeColumn = ({ value }) => {
  let retval = '';

  if (String(value) === TYPE_VALUE.BANK_TRANSFER) {
    retval = 'Bank Transfer';
  } else {
    retval = 'Qris';
  }

  return retval;
};

TypeColumn.propTypes = {
  value: PropTypes.string,
};

TypeColumn.defaultProps = {
  value: '',
};

export default TypeColumn;

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import update from 'immutability-helper';

import { debounce } from 'lodash';
import InputText from '../../../../components/form/InputText';
import Table from '../../../../components/retina/table/Table';
import SidePopup from '../../../../components/sidepopup/ContainerV2';
import Sidebar from './SideBar';

import SwitchBox from '../../../../components/form/SwitchBox';
import Select from '../../../../components/form/Select';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';
import {
    getDataVerification, getDetailBank, getDetailQris, updateDataVerification,
} from '../../../../data/majoolite';

import CoreHOC from '../../../../core/CoreHOC';
import {
    TABLE_META, FILTER_TYPE, STATUS_LIST, TYPE_LIST, TYPE_VALUE,
} from './config';
import { catchError } from '../../../../utils/helper';
import { FORM_TYPES } from '../../../../enum/form';

@CoreHOC
export default class DataVerification extends PureComponent {
    static propTypes = {
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }),
        assignCalendar: PropTypes.func,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
    };

    static defaultProps = {
        calendar: {
            start: '',
            end: '',
        },
        assignCalendar: () => {},
        notificationSystem: {
            addNotification: null,
        },
    };

    constructor(props) {
        super(props);

        const { calendar } = this.props;
        this.state = {
            filterTable: [
                { id: FILTER_TYPE.DATE_RANGE, value: calendar },
                { id: FILTER_TYPE.STATUS, value: '' },
                { id: FILTER_TYPE.TYPE, value: '' },
                { id: FILTER_TYPE.SEARCH, value: '' },
            ],
            isFetch: false,
            tableData: [],
            tableMeta: {
                pageSize: 10,
                pageIndex: 0,
                total: 0,
            },
        };
    }

    componentDidMount = async () => {
        const { assignCalendar } = this.props;
        const { tableMeta } = this.state;
        assignCalendar(null, null, (startDate, endDate) => {
            this.changeDateHandle(startDate, endDate);
        });
        this._onFetch({ ...tableMeta, pageIndex: 0 });
    }

    changeDateHandle = async (start, end) => {
        const { assignCalendar } = this.props;
        assignCalendar(start, end);

        const calendar = { start, end };
        this.updateCustomFilter(calendar, FILTER_TYPE.DATE_RANGE);
    }

    updateCustomFilter = (val, type) => {
        const { filterTable, tableMeta } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);
        this.setState({ filterTable: updatedFilterValue }, () => this._onFetch({ ...tableMeta, pageIndex: 0 }));
    }

    _onFetch = async (state) => {
        const { filterTable } = this.state;
        const { notificationSystem } = this.props;
        const {
            pageIndex, pageSize, sortAccessor, sortDirection,
        } = state;

        const { start, end } = getFilterValue(filterTable, FILTER_TYPE.DATE_RANGE);
        const search = getFilterValue(filterTable, FILTER_TYPE.SEARCH);
        const status = getFilterValue(filterTable, FILTER_TYPE.STATUS);
        const type = getFilterValue(filterTable, FILTER_TYPE.TYPE);

        const payload = {
            start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            limit: pageSize,
            page: Number(pageIndex) + 1,
            status,
            type,
            ...search && { search },
            ...sortAccessor && { sort_by: sortAccessor },
            ...sortDirection && { sort_type: sortDirection },
        };

        try {
            this.setState({ isFetch: true });
            const { data, meta: { total } } = await getDataVerification(payload);
            this.setState({
                isFetch: false,
                tableData: data || [],
                tableMeta: {
                    pageIndex,
                    pageSize,
                    total,
                    ...sortAccessor && { sortAccessor },
                    ...sortDirection && { sortDirection },
                },
            });
        } catch (e) {
            this.setState({ isFetch: false });
            notificationSystem.addNotification({
                title: 'Gagal!',
                message: catchError(e),
                level: 'error',
            });
        }
    }

    callEditDetailHandle = async ({
        original: {
            id, type,
        },
    }) => {
        const { notificationSystem } = this.props;

        try {
            if (type === TYPE_VALUE.BANK_TRANSFER) {
                const res = await getDetailBank({ id });
                const { data: form } = res;

                this.setState({
                    form,
                }, () => {
                    this.sidePop.showPopup();
                });
            } else {
                const res = await getDetailQris({ id });
                const { data: form } = res;

                const mapsData = await this.mapsRender(form);

                this.setState({
                    form,
                    mapsData,
                }, () => {
                    this.sidePop.showPopup();
                });
            }
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    mapsRender = async (data) => {
        const {
            business: {
                business_name: businessName,
                address, latitude, longitude,
                logo_url: logoUrl,
            },
        } = data;

        return {
            dataMarker: [
                {
                    usaha: businessName,
                    cabang_name: '',
                    logo: logoUrl,
                    alamat: address,
                    lat: Number(latitude),
                    lng: Number(longitude),
                },
            ],
            center: `${latitude},${longitude}`,
            zoom: 10,
        };
    }

    changeEvent = (key, val) => new Promise((resolve) => {
        const { form } = this.state;

        const newData = update(form, {
            [key]: {
                $set: val,
            },
        });

        this.setState({
            form: newData,
        }, () => {
            resolve();
        });
    })

    saveHandler = async () => {
        const { notificationSystem, showProgress, hideProgress } = this.props;
        const {
            form: { id, remark, status },
        } = this.state;

        try {
            showProgress();
            const payload = { status, remark };

            await updateDataVerification(id, payload);

            notificationSystem.addNotification({
                title: 'Berhasil Menyimpan Data',
                level: 'success',
            });

            this.table.forceRefetch();
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Failed to Update Data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            this.sidePop.hidePopup();
            hideProgress();
        }
    }

    render() {
        const {
            form, mapsData, filterTable,
            tableData, tableMeta: { pageIndex, pageSize, total }, isFetch,
        } = this.state;

        const statusFilter = getFilterValue(filterTable, FILTER_TYPE.STATUS);
        const typeFilter = getFilterValue(filterTable, FILTER_TYPE.TYPE);
        const keyword = getFilterValue(filterTable, FILTER_TYPE.SEARCH);

        return (
            <div>
                <section className="panel">
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-9">
                                <h4
                                    className="panel-title"
                                    style={{ paddingTop: '8px' }}
                                >
                                    Data Verification
                                </h4>
                            </div>
                            <div className="col-md-3">
                                <InputText
                                    classes="filter"
                                    placeholder="Cari ..."
                                    changeEvent={debounce(val => this.updateCustomFilter(val, FILTER_TYPE.SEARCH), 500)}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-9" style={{ textAlign: 'right' }}>
                                <SwitchBox
                                    dataset={STATUS_LIST}
                                    value={statusFilter}
                                    changeEvent={value => this.updateCustomFilter(value, FILTER_TYPE.STATUS)}
                                />
                            </div>
                            <div className="col-md-3">
                                <Select
                                    data={TYPE_LIST}
                                    placeholder="All Type"
                                    value={typeFilter}
                                    changeEvent={value => this.updateCustomFilter(value, FILTER_TYPE.TYPE)}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="panel-body">
                        <Table
                            columns={TABLE_META}
                            data={tableData}
                            pageIndex={pageIndex}
                            rowLimit={pageSize}
                            totalData={total}
                            fetchData={this._onFetch}
                            isLoading={isFetch}
                            searchQuery={keyword}
                            onRowClick={this.callEditDetailHandle}
                        />
                    </div>
                </section>
                <SidePopup
                    ref={(c) => { this.sidePop = c; }}
                    width={600}
                    type={FORM_TYPES.EDIT}
                    saveHandle={this.saveHandler}
                    render={({ validateInput }) => (
                        <Sidebar
                            data={form}
                            mapsData={mapsData}
                            validateInput={validateInput}
                            changeEvent={this.changeEvent}
                        />
                    )}
                />
            </div>
        );
    }
}

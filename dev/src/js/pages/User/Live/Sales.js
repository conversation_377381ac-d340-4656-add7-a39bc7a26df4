import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import CoreHOC from '../../../core/CoreHOC';

import BarChart from '../../../components/chart/BarChart';
import Table from '../../../components/table/v.2/Table';

import { getLiveSales } from '../../../data/users/live';
import { catchError, numSeparator } from '../../../utils/helper';
import { TABLE_META } from './config';

class Sales extends Component {
    static propTypes = {
      assignCalendar: PropTypes.func,
      assignButtons: PropTypes.func,
      windowHeight: PropTypes.number,
      notificationSystem: PropTypes.shape({
        push: PropTypes.func,
      }),
      assignFilterColoumn: PropTypes.func,
      assignRangeDate: PropTypes.func,
      showProgress: PropTypes.func.isRequired,
      hideProgress: PropTypes.func.isRequired,
    }

    static defaultProps = {
      assignCalendar: () => { },
      assignButtons: () => { },
      windowHeight: 740,
      notificationSystem: {
        addNotification: null,
      },
      assignFilterColoumn: () => { },
      assignRangeDate: () => { },
    }

    constructor(props) {
      super(props);

      this.state = {
        dataTable: [],
        chart: [],
      };
    }


    componentDidMount = () => {
      const {
        assignCalendar, assignButtons, assignFilterColoumn, assignRangeDate,
      } = this.props;
      assignCalendar(null, null, null);
      assignButtons([]);
      assignFilterColoumn([]);
      assignRangeDate([]);

      this.reloadData();
    }

    reloadData = async () => {
      const {
        notificationSystem, showProgress, hideProgress,
      } = this.props;

      showProgress();
      try {
        const res = await getLiveSales();
        const {
          data: {
            chart, table,
          },
        } = res;

        this.setState({
          chart,
          dataTable: table,
        });
      } catch (err) {
        notificationSystem.addNotification({
          title: 'Gagal mendapatkan data',
          message: catchError(err),
          level: 'error',
        });
      } finally {
        hideProgress();
      }
    }

    render() {
      const {
        dataTable, chart,
      } = this.state;

      const { windowHeight } = this.props;
      const heightPanel = ((windowHeight - 165) / 4);

      return (
        <div className="panel">
          <div className="dashboard">
            <div className="row">
              {
                chart.map((x, index) => (
                  <div className="col-xs-4" key={x.label}>
                    <BarChart
                      labels={x.listLabel}
                      dataChart={x.list}
                      title={x.label}
                      icon="ic-growth"
                      headerTitle={numSeparator(x.gap)}
                      headerSubtitle={parseFloat(x.gap_percent)}
                      headerDesc={x.label}
                      height={heightPanel + 50}
                      name={x.label.replace(/\s/g, '') + index}
                    />
                  </div>
                ))
              }
            </div>
          </div>
          <Table
            columns={TABLE_META}
            content={dataTable}
            rowEvent={val => this.callEditDetailHandler(val._original)}
            withWrapperRender={({ makeTable }) => (
              <section className="panel">
                <div className="panel-body">
                  {makeTable()}
                </div>
              </section>
            )}
          />
        </div>
      );
    }
}

const mapStateToProps = state => ({
  windowHeight: state.layout.windowHeight,
});

export default CoreHOC(connect(mapStateToProps)(Sales));

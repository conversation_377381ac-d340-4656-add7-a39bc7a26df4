import React from 'react';
import DateTimeColumn from '../../../../components/table/components/DateTimeColumn';

const USER_NEED_POS = ['', 'Ya, Butuh', 'Ragu-Ragu', 'Tidak / Belum Butuh'];
const tableColumn = [
    {
        Header: 'Submit Date',
        accessor: 'create_date',
        Cell: DateTimeColumn,
    },
    {
        Header: 'Campaign',
        accessor: 'campaign_name',
    },
    {
        Header: 'Leads Name',
        accessor: 'name',
    },
    {
        Header: 'Phone',
        accessor: 'phone',
    },
    {
        Header: 'Daerah Tempat Tinggal',
        accessor: 'city',
    },
    {
        Header: 'Business Name',
        accessor: 'business_name',
    },
    {
        Header: 'Business Category',
        accessor: 'business_category',
    },
    {
        Header: 'Business Address',
        accessor: 'alamat_bisnis',
    },
    {
        Header: 'Omset Per Bulan',
        accessor: 'omset',
    },
    {
        Header: 'Membutuhkan Aplikasi Kasir',
        accessor: 'is_need_pos',
        Cell: ({ data, value }) => {
            let param = data;
            if (value) {
                param = value;
            }
            if (Array.isArray(param)) {
                param = '';
            }
            return <span>{USER_NEED_POS[param]}</span>;
        },
    },
    {
        Header: 'Notes',
        accessor: 'extra_note',
    }
];

const selectAllCampaign = { id: '', name: 'All Campaign' };

const FILTERTYPE = {
    DATE: 'filterDate',
    CAMPAIGN: 'filterCampaign',
};

export {
    tableColumn, selectAllCampaign, FILTERTYPE, USER_NEED_POS,
};

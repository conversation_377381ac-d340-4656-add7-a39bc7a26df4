import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { FieldFeedback, FieldFeedbacks } from 'react-form-with-constraints';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/table/v.2/Table';
import SidePopup from '../../../components/sidepopup/ContainerV2';
import InputText from '../../../components/form/InputText';
import InputNumber from '../../../components/form/InputNumber';
import InputTextArea from '../../../components/form/InputTextArea';
import ModalPopup from '../../../components/form/ModalPopup';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';
import Switch from '../../../components/form/Switch';
import { getSupportCategory, addSupportCategory, updateSupportCategory } from '../../../data/sales/master';
import { TABLE_META } from './config/category';
import { catchError } from '../../../utils/helper';

class CategorySuppport extends Component {
  static propTypes = {
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    assignButtons: PropTypes.func,
  }

  static defaultProps = {
    notificationSystem: {
      addNotification: null,
    },
    router: {
      push: null,
    },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
    assignButtons: () => { },
  }

  constructor(props) {
    super(props);

    this.state = {

      supportCategoryList: [],
      type: 'edit',
      name: '',
      id: '',
      desc: '',
      active_status: false,
      pro_business: false,
      id_jenis: 0,
      titleSide: 'Add Support Category',
      seq: 0,
    };
  }

  componentWillMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignButtons([]);
    assignButtons([{ type: 'primary', content: <span> Add Support Category </span>, action: () => { this.callAddHandler(); } }]);
    this.getData();
  }

  getData() {
    const {
      router, notificationSystem,
    } = this.props;
    getSupportCategory().then((response) => {
      this.setState({
        supportCategoryList: response.data,
      });
      this.sidePop.hidePopup();
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'get data failed',
          message: '',
          level: 'error',
        });
      }
    });
  }

  callEditDetailHandler = (value) => {
    let stat = false;
    let jenis = false;

    if (value.active_status === '1') {
      stat = true;
    }

    if (value.pro_business === '1') {
      jenis = true;
    }

    this.setState({
      id: value.id,
      name: value.name,
      desc: value.desc,
      id_jenis: value.id_jenis,
      active_status: stat,
      pro_business: jenis,
      type: 'edit',
      seq: value.seq,
      titleSide: 'Update Support Category',
    });

    this.sidePop.showPopup();
  }

  callAddHandler = () => {
    const stat = true;
    this.setState({
      id: '',
      name: '',
      desc: '',
      id_jenis: 0,
      active_status: stat,
      pro_business: true,
      type: 'save',
      seq: 0,
      titleSide: 'Add Support Category',
    });

    this.sidePop.showPopup();
  }

  saveHandler = async (type) => {
    const {
      name, id, desc, id_jenis: idJenis, seq,
      active_status: activeStatus, pro_business: proBusiness,
    } = this.state;
    const parameter = {
      name,
      id,
      desc,
      id_jenis: idJenis,
      active_status: activeStatus,
      pro_business: proBusiness,
      seq,
    };

    const {
      notificationSystem,
    } = this.props;

    try {
      if (type === 'edit') {
        await updateSupportCategory(parameter);
        notificationSystem.addNotification({
          title: 'Success',
          message: 'Update support category success',
          level: 'success',
        });
      } else {
        await addSupportCategory(parameter);
        notificationSystem.addNotification({
          title: 'Success',
          message: 'Add support category success',
          level: 'success',
        });
      }
      this.getData();
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Update failed',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  handleChange = (val, type, e = null) => {
    this.setState({ [type]: val });
    if (e) {
      this.sidePop.validateInput(e.target);
    }
  }


  render() {
    const {
      supportCategoryList, type, titleSide, name, desc, seq,
      active_status: activeStatus, pro_business: proBusiness,
      imgSrc, captionGambar, displayed,
    } = this.state;
    return (
      <div>
        <Table
            columns={TABLE_META}
            content={supportCategoryList}
            rowEvent={val => this.callEditDetailHandler(val._original)}
            withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
                <section className="panel">
                  <div className="panel-heading table-header">
                    <div className="row">
                      <div className="col-md-2">
                        <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                          Support Category
                        </h4>
                      </div>
                      <div className="col-md-10">
                        <div className="col-md-1" style={{ paddingLeft: '0px', float: 'right' }}>
                          <PageSize />
                        </div>
                        <div style={{ paddingRight: '25px', float: 'right' }}>
                          <InputSearch />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="panel-body">
                    {makeTable()}
                  </div>
                </section>
            )}
        />
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          type="edit"
          saveHandle={() => this.saveHandler(type)}
          render={({ show }) => {
            if (show) {
              return (
                  <div>
                    <h4 className="side-popup-title">
                      {titleSide}
                    </h4>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputText
                            label="Name"
                            placeholder="Name"
                            value={name}
                            changeEvent={(value, e) => this.handleChange(value, 'name', e)}
                            name="name_form"
                        />
                        <FieldFeedbacks for="name_form">
                          <FieldFeedback when={val => val === ''}>Name tidak boleh kosong</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputTextArea label="Description" placeholder="Description" value={desc} changeEvent={value => this.setState({ desc: value })} />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputNumber
                            label="Seq"
                            placeholder="Seq"
                            value={seq}
                            changeEvent={(value, e) => this.handleChange(value, 'seq', e)}
                            name="seq_form"
                        />
                        <FieldFeedbacks for="seq_form">
                          <FieldFeedback when={val => val === ''}>Seq tidak boleh kosong</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-6 text-left">
                        <span className="control-label" style={{ paddingTop: '12px' }}>Status</span>
                      </div>
                      <div className="col-sm-6 text-right">
                        <Switch className="text-right" checked={activeStatus} changeEvent={value => this.setState({ active_status: value })} />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-6 text-left">
                        <span className="control-label" style={{ paddingTop: '12px' }}>Is Subscribe</span>
                      </div>
                      <div className="col-sm-6 text-right">
                        <Switch className="text-right" checked={proBusiness} valueTrue="True" valueFalse="False" changeEvent={value => this.setState({ pro_business: value })} />
                      </div>
                    </div>
                  </div>
              );
            }
            return (null);
          }}
        />

        <DeleteConfirm
          title="Hapus Tittle"
          confirmText="Ya, Hapus"
          cancelText="Tidak, Simpan"
          confirmHandle={() => { this.removeHandler(); }}
          ref={(c) => { this.deleteConfirm = c; }}
        >
          Menghapus pembelian akan menghilangkan secara permanen
          dari daftar Produk anda. Tindakan ini tidak bisa dibatalkan
        </DeleteConfirm>

        <ModalPopup
          src={imgSrc}
          name={captionGambar}
          clickEvent={() => { this.removeModal(); }}
          displayed={displayed}
        />
      </div>
    );
  }
}

export default CoreHOC(CategorySuppport);

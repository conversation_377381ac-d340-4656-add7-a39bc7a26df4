import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';
import update from 'immutability-helper';

/* COMPONENTS */
import Table from '../../../components/table/v.2/Table';

import Select from '../../../components/form/Select';
import InputText from '../../../components/form/InputText';

import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';
import CalendarWithPeriodHelper from '../../../components/form/CalendarWithPeriodHelper';

import FeedbackSidebar from './Sidebar/FeedbackSidebar';

/* DATA */
import {
  getFeedback, updateFeedback, deleteFeedback, getBusinessTypeList, getSupportTypeList,
} from '../../../data/sales/support';

/* CONFIG */
import {
  TABLE_META, STATUS_ENUM, SOURCE_ENUM_LIST,
} from './config/Feedback';
import { catchError } from '../../../utils/helper';

import CoreHOC from '../../../core/CoreHOC';

class Feedback extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({}),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
  }

  static defaultProps = {
    assignCalendar: () => {},
    assignButtons: () => {},
    notificationSystem: null,
    assignFilterColoumn: () => {},
    assignRangeDate: () => {},
    calendar: {
      start: '',
      end: '',
    },
  }

  constructor(props) {
    super(props);

    const { calendar: { start, end } } = this.props;
    this.state = {
      feedbackList: [],
      statusFilterList: [],
      statusEditList: [],
      businessTypeList: [],
      supportTypeList: [],
      form: {
        usaha_name: '',
        name: '',
        email: '',
        note: '',
        respon: '',
        id_status: '',
        outlet: '',
        pengubah: '',
        id_update: '',
      },
      filterTable: {
        calendar: {
          start,
          end,
        },
        selectedStatus: STATUS_ENUM.ALL,
        selectedSource: '',
        selectedBusinessType: '',
        selectedSupportType: '',
        textKeyword: '',
      },
    };
  }

  componentWillMount = async () => {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignButtons([]);
    assignCalendar(null, null, null);

    await this.getDropDownData();
  }

  getDropDownData = async () => {
    const { notificationSystem } = this.props;

    try {
      const { data: businessTypeList } = await getBusinessTypeList();
      const { data: supportTypeList } = await getSupportTypeList();

      this.setState({
        businessTypeList: [{ id: "", name: "All Business Type"}, ...businessTypeList],
        supportTypeList: [{ id: "", name: "All Support Type"}, ...supportTypeList],
      }, () => {
        this.getData();
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Get data Business List Failed',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  getData = async () => {
    const { notificationSystem, hideProgress } = this.props;
    const {
      filterTable: {
        calendar: { start, end },
        selectedStatus, selectedSource,
        textKeyword, selectedBusinessType,
        selectedSupportType,
      },
    } = this.state;

    const filter = JSON.stringify({
      ...selectedSource !== '' && {
        source: selectedSource,
      },
      ...selectedBusinessType !== '' && {
        business_type: selectedBusinessType,
      },
      ...selectedSupportType !== '' && {
        support_type: selectedSupportType,
      },
      ...selectedStatus !== STATUS_ENUM.ALL && {
        status: selectedStatus,
      },
    });

    const param = {
      start: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      ...textKeyword !== '' && {
        search: textKeyword,
      },
      filter,
    };

    try {
      const {
        data: {
          status: statusFilterList, summary: feedbackList,
        },
      } = await getFeedback(param);

      const statusEditList = statusFilterList.filter(({ id }) => id !== 0).map(({ id, label }) => ({ value: id, label }));

      this.setState({
        feedbackList,
        statusFilterList,
        statusEditList,
      });
    } catch (error) {
      notificationSystem.addNotification({
        title: 'Get data feedback failed',
        message: catchError(error),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  changeCalendarHandler = (start, end) => {
    const { assignCalendar } = this.props;
    assignCalendar(start, end);

    this.updateCustomFilter({ start, end }, 'calendar');
  };

  filterKeyword = (val) => {
    const { filterTable } = this.state;

    const newFilterTable = update(filterTable, {
      textKeyword: { $set: val },
    });

    this.setState({
      filterTable: newFilterTable,
    }, () => {
      if (this.timeoutMenu) clearTimeout(this.timeoutMenu);
      this.timeoutMenu = setTimeout(() => {
          this.getData();
        }, 250);
    });
  }

  updateCustomFilter = (val, key) => {
      const { filterTable } = this.state;

      const newFilterTable = update(filterTable, {
        [key]: { $set: val },
      });

      this.setState({
        filterTable: newFilterTable,
      }, () => {
        this.getData();
      });
  }

  callEditDetailHandler = ({ _original: value }) => {
    let idStatus = value.id_status;

    if (
      value.id_status !== STATUS_ENUM.NEW
      && value.id_status !== STATUS_ENUM.READ
      && value.id_status !== STATUS_ENUM.SOLVE
    ) {
      idStatus = STATUS_ENUM.ALL;
    }

    this.setState({
      form: {
        usaha_name: value.usaha_name,
        name: value.name,
        email: value.email,
        note: value.note,
        no_whatsapp: value.no_whatsapp || '',
        respon: value.respon || '',
        id_status: idStatus.toString(),
        outlet: value.outlet,
        pengubah: value.pengubah,
        id_update: value.feedback_id,
      },
    });
    this.sidePop.showPopup();
  }

  saveHandler = async () => {
    const {
      showProgress, hideProgress, notificationSystem,
    } = this.props;
    const {
      form: {
        respon, id_status: idStatus, id_update: idUpdate,
      },
    } = this.state;

    showProgress();
    try {
      const payload = { respon, id_status: idStatus, id_update: idUpdate };

      await updateFeedback(payload);

      notificationSystem.addNotification({
        title: 'Success',
        message: 'Update data feedback success',
        level: 'success',
      });
      this.sidePop.hidePopup();
      this.getData();
    } catch (error) {
      notificationSystem.addNotification({
        title: 'Update data feedback failed',
        message: catchError(error),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  callRemoveHandler = () => {
    this.sidePop.hidePopup();
    this.deleteConfirm.showPopup();
  }

  removeHandler = async () => {
    const {
      showProgress, hideProgress, notificationSystem,
    } = this.props;
    const {
      form: {
        id_update: idUpdate,
      },
    } = this.state;

    showProgress();
    try {
      const parameter = { id: idUpdate, status: '9' };

      await deleteFeedback(parameter);

      this.deleteConfirm.hidePopup();
      this.getData();

      notificationSystem.addNotification({
        title: 'Success',
        message: 'Remove data feedback success',
        level: 'success',
      });
    } catch (error) {
      notificationSystem.addNotification({
        title: 'Remove data feedback failed',
        message: catchError(error),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  changeEvent = (key, value) => {
    const { form } = this.state;

    const newForm = update(form, {
        [key]: {
            $set: value,
        },
    });


    this.setState({
        form: newForm,
    });
  }

  render() {
      const {
        feedbackList, statusFilterList,
        businessTypeList, supportTypeList,
        form, statusEditList,
        filterTable: {
          calendar: { start, end },
          selectedStatus, selectedSource, selectedBusinessType,
          selectedSupportType, textKeyword,
        },
      } = this.state;
      const { name } = form;

      return (
          <div>
            <Table
                ref={(c) => {
                    this.table = c;
                }}
                columns={TABLE_META}
                content={feedbackList}
                rowEvent={data => this.callEditDetailHandler(data)}
                withWrapperRender={({ makeTable, PageSize }) => (
                  <section className="panel">
                      <div className="panel-heading table-header">
                        <div className="row">
                            <div className="col-md-12">
                                <h4 className="panel-title" style={{ paddingTop: '8px' }}>Feedback</h4>
                            </div>
                        </div>
                      </div>
                      <div className="panel-heading table-header">
                        <div className="row">
                          <div className="col-md-6">
                            <CalendarWithPeriodHelper
                              startDate={start}
                              endDate={end}
                              changeEvent={this.changeCalendarHandler}
                              styles={{
                                width: '100%',
                              }}
                            />
                          </div>
                          <div className="col-md-3">
                            <Select
                              data={SOURCE_ENUM_LIST}
                              value={selectedSource}
                              changeEvent={value => this.updateCustomFilter(value, 'selectedSource')}
                            />
                          </div>
                          <div className="col-md-3">
                            <Select
                              data={businessTypeList}
                              value={selectedBusinessType}
                              placeholder="Busniness Type"
                              changeEvent={value => this.updateCustomFilter(value, 'selectedBusinessType')}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="panel-heading table-header">
                        <div className="row">
                          <div className="col-md-4">
                            <Select
                              data={supportTypeList}
                              value={selectedSupportType}
                              placeholder="Support Type"
                              changeEvent={value => this.updateCustomFilter(value, 'selectedSupportType')}
                            />
                          </div>
                          <div className="col-md-4">
                            <Select
                              data={statusFilterList}
                              value={selectedStatus}
                              changeEvent={value => this.updateCustomFilter(value, 'selectedStatus')}
                            />
                          </div>
                          <div className="col-md-3">
                            <InputText
                              placeholder="Cari ..."
                              value={textKeyword}
                              changeEvent={value => this.filterKeyword(value)}
                            />
                          </div>
                          <div className="col-md-1">
                            <PageSize />
                          </div>
                        </div>
                      </div>
                      <div className="panel-body">
                        {makeTable()}
                      </div>
                  </section>
              )}
            />

            <FeedbackSidebar
              data={form}
              ref={(c) => {
                  this.sidePop = c;
              }}
              changeEvent={this.changeEvent}
              statusList={statusEditList}
              saveHandle={this.saveHandler}
              removeHandle={this.callRemoveHandler}
            />

            <DeleteConfirm
              title="Remove Feedback List"
              confirmHandle={this.removeHandler}
              ref={(c) => {
                  this.deleteConfirm = c;
              }}
            >
              Feed back from
              {` ${name} `}
              will be removed from feedback List.
            </DeleteConfirm>
          </div>
      );
  }
}

export default CoreHOC(Feedback);

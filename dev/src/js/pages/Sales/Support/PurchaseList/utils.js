import React from 'react';
import moment from 'moment';
import NumberDateTimeColumn from '../../../../components/table/components/NumberDateTimeColumn';
import PriceColumn from '../../../../components/table/components/PriceColumn';

export const tableColumns = [
    {
        Header: 'Business Time',
        accessor: 'waktu_bisnis_number',
        Cell: ({ original }) => (
            <div>
                {NumberDateTimeColumn({ value: original.waktu_bisnis })}
                <br />
                {original.actor ? `By ${original.actor}` :''}
            </div>
        ),
        minWidth: 200,
    },
    {
        Header: 'Invoice CRM',
        accessor: 'invoice_crm',
        Cell: ({ original }) => original.invoice_crm ? original.invoice_crm : '-',
        minWidth: 200,
    },
    {
        Header: 'User',
        accessor: 'user',
    },
    {
        Header: 'Company',
        accessor: 'usaha',
    },
    {
        Header: 'Outlet',
        accessor: 'cabang',
    },
    {
        Header: 'Email Owner',
        accessor: 'user_email',
        minWidth: 200,
    },
    {
        Header: 'Email',
        accessor: 'email',
        minWidth: 200,
    },
    {
        Header: 'Phone',
        accessor: 'telepon',
    },
    {
        Header: 'Support Type',
        accessor: 'jenis',
        minWidth: 200,
    },
    {
        Header: 'Nominal',
        accessor: 'nominal',
        Cell: PriceColumn,
    },
    {
        Header: 'Status',
        accessor: 'status',
    },
];

export const TABLE_FILTER_TYPE = {
    CALENDAR: 'filterCalendar',
    STATUS_ID: 'filterStatusId',
    SUPPORT_TYPE: 'filterSupport'
};

export const reformatCalendar = (val, asFormat = 'DD MMMM YYYY') => moment(val, 'DD/MM/YYYY').format(asFormat);

export const STATUS_TYPE = {
    ALL: 0,
};

import React, { Component } from 'react';
import PropTypes from 'prop-types';

import Table, { getFilterValue, getUpdatedFilterValue } from '../../../../components/table/v.2/Table';
import Select from '../../../../components/form/Select';

import { getPenjualan } from '../../../../data/sales/support';

import {
 tableColumns, TABLE_FILTER_TYPE, reformatCalendar, STATUS_TYPE,
} from './utils';
import { catchError } from '../../../../utils/helper';

class PurchaseList extends Component {
    constructor(props) {
        super(props);
        const { calendar: { start, end } } = props;

        this.state = {
            filterTable: [
                { id: TABLE_FILTER_TYPE.CALENDAR, value: { start, end } },
                { id: TABLE_FILTER_TYPE.STATUS_ID, value: STATUS_TYPE.ALL },
                { id: TABLE_FILTER_TYPE.SUPPORT_TYPE, value: '' },
            ],
            listSupport: [],
        };
    }

    componentDidUpdate({ listCategorySupport: pLists }) {
        const { listCategorySupport } = this.props;
        if (pLists !== listCategorySupport) {
            const layananBerlangganan = listCategorySupport.find(x => String(x.id) === "31");
            const layananTambahan = listCategorySupport.find(x => String(x.id) === "2");
            const supportOptions = layananBerlangganan.list.concat(layananTambahan.list);
            
            const listSupport = supportOptions.map(x => {
                return [x.id, x.name];
            });
            listSupport.unshift(['', 'All']);

            this.setState({
                listSupport,
            })
        }
    }

    componentWillReceiveProps = (nextProps) => {
        const newState = this.handleDeriveFilterCalendar(nextProps, this.state);

        if (newState) this.setState(newState);
    }

    handleDeriveFilterCalendar = (props, state) => {
        let needToUpdateState = false;

        const filterCalendar = getFilterValue(state.filterTable, TABLE_FILTER_TYPE.CALENDAR);

        if (filterCalendar.start !== props.calendar.start) {
            needToUpdateState = true;
        }

        if (filterCalendar.end !== props.calendar.end) {
            needToUpdateState = true;
        }

        if (!needToUpdateState) return null;

        const updatedFilterValue = getUpdatedFilterValue(state.filterTable, TABLE_FILTER_TYPE.CALENDAR, {
            start: props.calendar.start,
            end: props.calendar.end,
        });

        return {
            ...state,
            filterTable: updatedFilterValue,
        };
    };

    refetchTable = () => {
        this.table.forceRefetch();
      }

    handleFetchData = async (state) => {
        const { notificationSystem, handleUpdateStatusList } = this.props;
        const {
            pageSize, page, sorted, pages, filtered,
        } = state;

        const pageCount = pages > -1 ? pages : -1;
        let retval = { data: [], pageCount, err: null };

        try {
            let sort,
                order;

            if (sorted.length > 0) {
                const { [sorted.length - 1]: { id, desc } } = sorted;
                order = id;
                sort = (!desc) ? 'ASC' : 'DESC';
            }

            const filterCalendar = getFilterValue(filtered, TABLE_FILTER_TYPE.CALENDAR);
            const filterKeyword = getFilterValue(filtered, 'all');
            const currentStatusId = getFilterValue(filtered, TABLE_FILTER_TYPE.STATUS_ID);
            const filterSupportType = getFilterValue(filtered, TABLE_FILTER_TYPE.SUPPORT_TYPE);

            const payload = {
                start: reformatCalendar(filterCalendar.start, 'YYYY-MM-DD'),
                end: reformatCalendar(filterCalendar.end, 'YYYY-MM-DD'),
                resultPerpage: pageSize,
                page: page + 1,
                id_status: currentStatusId,
                ...filterKeyword && { keyword: filterKeyword },
                ...sort && {
                    isAsc: sort,
                },
                ...order && {
                    column: order,
                },
                support_type: filterSupportType,
            };

            const res = await getPenjualan(payload);
            retval = {
                ...retval,
                data: res.data.summary,
                pageCount: Math.ceil(Number(res.data.total) / Number(pageSize)),
            };
            const statusList = res.data.status;
            handleUpdateStatusList(statusList);
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Get Data List Failed',
                message: catchError(e),
                level: 'error',
            });
            retval = { ...retval, err: e };
        }

        return retval;
    }

    handleChangeFilterTable = (val, type) => {
        const { filterTable } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);

        this.setState({ filterTable: updatedFilterValue });
    }

    render() {
        const { filterTable, listSupport } = this.state;
        const {
            onRowClick, statusList,
        } = this.props;

        const currentStatusId = getFilterValue(filterTable, TABLE_FILTER_TYPE.STATUS_ID);
        const currentSupportType = getFilterValue(filterTable, TABLE_FILTER_TYPE.SUPPORT_TYPE);

        return (
            <Table
                ref={(c) => { this.table = c; }}
                onFetch={this.handleFetchData}
                columns={tableColumns}
                rowEvent={val => onRowClick(val)}
                filters={filterTable}
                withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
                    <section className="panel">
                        <div className="panel-heading table-header">
                            <div className="row">
                                <div className="col-md-3">
                                    <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                                        Purchase
                                    </h4>
                                </div>
                                <div className="col-md-3">
                                    <Select
                                        data={statusList}
                                        value={currentStatusId}
                                        changeEvent={(val) => { this.handleChangeFilterTable(val, TABLE_FILTER_TYPE.STATUS_ID); }}
                                    />
                                </div>
                                <div className="col-md-3">
                                    <Select
                                        data={listSupport}
                                        value={currentSupportType}
                                        changeEvent={(val) => { this.handleChangeFilterTable(val, TABLE_FILTER_TYPE.SUPPORT_TYPE); }}
                                    />
                                </div>
                                <div className="col-md-3">
                                    <PageSize />
                                    <div className="filter-container">
                                        <InputSearch />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="panel-body">
                            {makeTable()}
                        </div>
                    </section>
                )}
            />
        );
    }
}

PurchaseList.propTypes = {
    calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
    }),
    notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
    }),
    onRowClick: PropTypes.func,
    handleUpdateStatusList: PropTypes.func,
    statusList: PropTypes.arrayOf(
        PropTypes.arrayOf(
            PropTypes.oneOfType([
                PropTypes.number,
                PropTypes.string,
            ]),
        ),
    ),
    listCategorySupport: PropTypes.arrayOf(PropTypes.shape({}))
};

PurchaseList.defaultProps = {
    calendar: {
        start: '',
        end: '',
    },
    notificationSystem: ({
        addNotification: () => {},
    }),
    onRowClick: () => {},
    handleUpdateStatusList: PropTypes.func,
    statusList: [],
    listCategorySupport: [],
};

export default PurchaseList;

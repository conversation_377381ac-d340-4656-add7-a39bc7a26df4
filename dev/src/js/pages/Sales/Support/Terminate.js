import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

/* COMPONENTS */
import update from 'immutability-helper';
import Table, { getFilterValue, getUpdatedFilterValue } from '../../../components/table/v.2/Table';
import Select from '../../../components/form/Select';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';

import TerminateSidebar from './Sidebar/TerminateSidebar';

/* DATA */
import {
    getTerminate, getTerminateDetail, updateTerminate,
} from '../../../data/sales/support';

/* CONFIG */
import { catchError } from '../../../utils/helper';
import CoreHOC from '../../../core/CoreHOC';
import {
    TABLE_META, STATUS_LIST, PAYMENT_LIST, FILTER_TYPE,
} from './config/Terminate';

@CoreHOC
export default class Promo extends PureComponent {
    static propTypes = {
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }),
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        assignCalendar: PropTypes.func,
    };

    static defaultProps = {
        calendar: {
            start: '',
            end: '',
        },
        notificationSystem: {
            addNotification: null,
        },
        assignCalendar: () => {
        },
    };

    constructor(props) {
        super(props);

        const { calendar } = this.props;
        this.state = {
            form: {
                id: '',
                supportCategoryName: '',
                supportId: '',
                supportName: '',
                detail: '',
                categoryName: '',
                outletList: {
                    outletId: '',
                    outletName: '',
                    manager: '',
                    activeExp: '',
                },
                userId: '',
                note: '',
                pic: '',
                email: '',
            },
            filterTable: [
                { id: FILTER_TYPE.DATE_RANGE, value: calendar },
                { id: FILTER_TYPE.STATUS, value: 0 },
                { id: FILTER_TYPE.PAYMENT, value: 0 },
            ],
        };
    }

    componentWillMount = async () => {
        const { assignCalendar } = this.props;

        assignCalendar(null, null, (startDate, endDate) => {
            this.changeDateHandle(startDate, endDate);
        });
    }

    _onFetch = async (state) => {
        const { notificationSystem } = this.props;
        const {
            page, pages, sorted, filtered, pageSize,
        } = state;
        const { length } = sorted;

        const { start, end } = getFilterValue(filtered, FILTER_TYPE.DATE_RANGE);
        const statusFilter = getFilterValue(filtered, FILTER_TYPE.STATUS);
        const paymentFilter = getFilterValue(filtered, FILTER_TYPE.PAYMENT);

        let payload = {
            start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
            status: statusFilter,
            paymentStatus: paymentFilter,
            limt: pageSize,
            page: Number(page) + 1,
        };

        // get sort params
        if (length > 0) {
            const { [length - 1]: { id, desc } } = sorted;
            payload = { ...payload, ...{ order: id, sort: desc ? 'DESC' : 'ASC' } };
        }

        // get filter params
        if (filtered && filtered.length > 0) {
            const filterAll = filtered.find(data => data.id === 'all');
            if (filterAll && filterAll.value !== '') {
                payload = {
                    ...payload,
                    search: filterAll.value,
                };
            }
        }

        let err = null,
            retval = {
                data: [],
                pageCount: pages > -1 ? pages : -1,
            };

        try {
            const res = await getTerminate(payload);
            const { data, meta: { last_page: pageCount } } = res;

            retval = { data, pageCount };
        } catch (e) {
            err = e;
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        }
        retval = { ...retval, ...{ err } };

        return retval;
    }

    callEditDetailHandle = async ({ _original: { id } }) => {
        const { notificationSystem } = this.props;

        try {
            const res = await getTerminateDetail({}, id);
            const {
                data: {
                    support_category_name: supportCategoryName,
                    support_id: supportId,
                    support_name: supportName,
                    support_detail: detail,
                    company: categoryName,
                    outlet_id: outletId,
                    outlet_name: outletName,
                    user_id: userId,
                    user_name: manager,
                    expired_date: activeExp,
                    pic_email: email,
                    pic_name: pic,
                    note,
                },
            } = res;

            this.setState({
                form: {
                    id,
                    supportCategoryName,
                    supportId,
                    supportName,
                    detail,
                    categoryName,
                    outletList: {
                        outletId,
                        outletName,
                        manager,
                        activeExp,
                    },
                    userId,
                    pic,
                    email,
                    note: note || '',
                },
            }, () => {
                this.sidePop.showPopup();
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    changeDateHandle = async (start, end) => {
        const { assignCalendar } = this.props;
        assignCalendar(start, end);

        const calendar = { start, end };
        this.updateCustomFilter(calendar, 'dateRange');
    }

    selectFilterHandle = async (val, type) => {
        this.updateCustomFilter(val, type);
    }

    updateCustomFilter = (val, type, callback = () => {}) => {
        const { filterTable } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);
        this.setState({ filterTable: updatedFilterValue }, () => callback);
    }

    changeEvent = (key, value) => {
        const { form } = this.state;
        const newForm = update(form, {
            [key]: {
                $set: value,
            },
        });

        this.setState({
            form: newForm,
        });
    }

    saveHandle = () => {
        this.confirmPopup.showPopup();
    }

    submitHandle = async () => {
        const { notificationSystem } = this.props;
        const { form } = this.state;
        const {
            userId, supportId, outletList: { outletId }, note,
        } = form;

        const payload = {
            support_id: supportId,
            user_id: userId,
            outlet_id: outletId,
            note,
        };

        try {
            await updateTerminate(payload);
            this.confirmPopup.hidePopup();
            this.sidePop.hidePopup();
            notificationSystem.addNotification({
                title: 'Berhasil memperbarui data',
                level: 'error',
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal memperbarui data',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    render() {
        const { notificationSystem } = this.props;
        const {
            form, filterTable,
        } = this.state;

        const statusFilter = getFilterValue(filterTable, FILTER_TYPE.STATUS);
        const paymentFilter = getFilterValue(filterTable, FILTER_TYPE.PAYMENT);

        return (
            <div>
                <Table
                    ref={(c) => {
                        this.table = c;
                    }}
                    columns={TABLE_META}
                    filters={filterTable}
                    rowEvent={this.callEditDetailHandle}
                    withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
                        <section className="panel">
                            <div className="panel-heading table-header">
                                <div className="row">
                                    <div className="col-md-3">
                                        <h4 className="panel-title" style={{ paddingTop: '8px' }}>Termination</h4>
                                    </div>
                                    <div className="col-md-3">
                                        <Select
                                            data={STATUS_LIST}
                                            value={statusFilter}
                                            changeEvent={value => this.selectFilterHandle(value, FILTER_TYPE.STATUS)}
                                        />
                                    </div>
                                    <div className="col-md-3">
                                        <Select
                                            data={PAYMENT_LIST}
                                            value={paymentFilter}
                                            changeEvent={value => this.selectFilterHandle(value, FILTER_TYPE.PAYMENT)}
                                        />
                                    </div>
                                    <div className="col-md-2">
                                        <InputSearch />
                                    </div>
                                    <div className="col-md-1">
                                        <PageSize />
                                    </div>
                                </div>
                            </div>
                            <div className="panel-body">
                                {makeTable()}
                            </div>
                        </section>
                    )}

                    // server-side
                    onFetch={this._onFetch}
                />

                <TerminateSidebar
                    data={form}
                    ref={(c) => {
                        this.sidePop = c;
                    }}
                    notificationSystem={notificationSystem}
                    changeEvent={this.changeEvent}
                    saveHandle={() => {
                        this.saveHandle();
                    }}
                />

                <DeleteConfirm
                    ref={(c) => {
                        this.confirmPopup = c;
                    }}
                    title="Confirmation"
                    confirmText="Yes, Terminate"
                    cancelText="Cancel"
                    confirmHandle={this.submitHandle}
                >
                    Are You Sure to Terminate?
                </DeleteConfirm>
            </div>
        );
    }
}

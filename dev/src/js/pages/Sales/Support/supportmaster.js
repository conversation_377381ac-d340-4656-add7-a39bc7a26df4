import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { FieldFeedbacks, FieldFeedback } from 'react-form-with-constraints';

import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/table/v.2/Table';
import SidePopup from '../../../components/sidepopup/ContainerV2';
import InputText from '../../../components/form/InputText';
import InputNumber from '../../../components/form/InputNumber';
import InputTextArea from '../../../components/form/InputTextArea';
import InputSelect from '../../../components/form/InputSelect';
import ModalPopup from '../../../components/form/ModalPopup';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';
import Switch from '../../../components/form/Switch';
import Autocomplete from '../../../components/form/Autocomplete';
import { getSupportMaster, updateSupportMaster, addSupportMaster } from '../../../data/sales/master';
import { getCategoryActive } from '../../../data/sales';
import { TABLE_META, SATUAN_DURATION } from './config/supportmaster';
import { catchError } from '../../../utils/helper';

class Supportmaster extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    notificationSystem: {
      addNotification: () => {},
    },
    router: {
      push: () => {},
    },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
  }

  constructor(props) {
    super(props);

    this.state = {

      supportList: [],
      category: [],
      type: 'edit',
      id: '',
      name: '',
      subtitle: '',
      desc: '',
      hargaSpesial: '',
      harga: '',
      diskon: '',
      qty: '1',
      duration: 0,
      categoryTerpilih: '',
      status: true,
      titleSide: 'Add Support',
      satuan: 'Bulan',
      unit: '',
      seq: 0,
      supportMasterActive: [],
      supportMasterActiveTerpilih: '',
      additionalQty: '',
      isNeedLocation: false,
      biayaTambahan: 0,
      isForSale: false,
    };
  }

  componentWillMount() {
    const {
      notificationSystem, assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([{ type: 'primary', content: <span> Add Support </span>, action: () => { this.callAddHandler(); } }]);
    this.getData();
    getCategoryActive().then((response) => {
      this.setState({
        category: response.data,
      });
    }).catch((err) => {
      notificationSystem.addNotification({
        title: 'Get data failed',
        message: catchError(err),
        level: 'error',
      });
    });
  }

  getData = async () => {
    const {
      notificationSystem,
    } = this.props;
    try {
      const response = await getSupportMaster();
      this.setState({
        supportList: response.data,
      });
      this.sidePop.hidePopup();
      this.deleteConfirm.hidePopup();
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Get data failed',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  callEditDetailHandler = (value) => {
    let status = false;
    const { supportList } = this.state;
    status = value.status === '1';
    const dataSUpport = supportList;
    const dataSUpportActive = dataSUpport.filter(d => d.status === '1');


    this.setState({
      id: value.id,
      name: value.name,
      subtitle: value.subtitle,
      desc: value.desc,
      hargaSpesial: value.harga_spesial,
      harga: value.harga,
      diskon: value.diskon || 0,
      qty: value.qty || 0,
      duration: value.duration_qty,
      categoryTerpilih: value.category_terpilih,
      status,
      satuan: value.duration,
      type: 'edit',
      titleSide: 'Update Support',
      unit: value.unit,
      seq: value.seq,
      supportMasterActive: dataSUpportActive.filter(d => d.id !== value.id),
      supportMasterActiveTerpilih: value.additional,
      additionalQty: value.additional_qty,
      isNeedLocation: value.is_need_location !== '0',
      biayaTambahan: value.biaya_tambahan,
      isForSale: value.is_for_sale === '1',
    }, () => {
      this.sidePop.showPopup();
    });
  }

  callAddHandler = () => {
    const { supportList } = this.state;
    const dataSUpport = supportList;

    this.setState({
      id: '',
      name: '',
      subtitle: '',
      desc: '',
      hargaSpesial: '',
      harga: '',
      diskon: '',
      qty: '1',
      duration: '',
      categoryTerpilih: '',
      status: true,
      type: 'add',
      titleSide: 'Add Support',
      unit: '',
      seq: 0,
      supportMasterActive: dataSUpport.filter(d => d.status === '1'),
      additionalQty: '',
      supportMasterActiveTerpilih: '',
      isNeedLocation: false,
      isForSale: true,
      biayaTambahan: 0,
    }, () => {
      this.sidePop.showPopup();
    });
  }

  saveHandler = async () => {
    const {
     isNeedLocation, type, isForSale,
      status, seq, additionalQty, supportMasterActiveTerpilih, categoryTerpilih, satuan, duration, biayaTambahan,
      qty, diskon, hargaSpesial, harga, unit, desc, subtitle, name, id,
    } = this.state;
    const {
      notificationSystem,
    } = this.props;

    const parameter = {
      id,
      name,
      subtitle,
      desc,
      harga_spesial: hargaSpesial,
      harga,
      diskon,
      qty,
      duration_qty: duration,
      satuan,
      unit,
      category_terpilih: categoryTerpilih,
      status,
      seq,
      additional: supportMasterActiveTerpilih,
      additional_qty: additionalQty,
      is_need_location: isNeedLocation,
      biaya_tambahan: biayaTambahan,
      isForSale,
    };

    try {
      if (type === 'edit') {
        await updateSupportMaster(parameter);
        notificationSystem.addNotification({
          title: 'Berhasil',
          message: 'Update support success',
          level: 'success',
        });
      } else {
        await addSupportMaster(parameter);
        notificationSystem.addNotification({
          title: 'Berhasil',
          message: 'Add support success',
          level: 'success',
        });
      }
      this.getData();
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Update support failed',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  handleChange = (val, type, e = null) => {
    this.setState({ [type]: val });
    if (e) {
      this.sidePop.validateInput(e.target);
    }
  }

  render() {
    const {
      titleSide, supportMasterActive, displayed, captionGambar, imgSrc, isNeedLocation,
      status, seq, additionalQty, supportMasterActiveTerpilih, categoryTerpilih, category, satuan, duration, biayaTambahan,
      qty, diskon, hargaSpesial, harga, unit, desc, subtitle, name, type, supportList, isForSale,
    } = this.state;
    return (
      <div>
        <Table
            columns={TABLE_META}
            content={supportList}
            rowEvent={val => this.callEditDetailHandler(val._original)}
            withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
                <section className="panel">
                  <div className="panel-heading table-header">
                    <div className="row">
                      <div className="col-md-2">
                        <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                          Pembelian
                        </h4>
                      </div>
                      <div className="col-md-10">
                        <div className="col-md-1" style={{ paddingLeft: '0px', float: 'right' }}>
                          <PageSize />
                        </div>
                        <div style={{ paddingRight: '20px', float: 'right' }}>
                          <InputSearch />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="panel-body">
                    {makeTable()}
                  </div>
                </section>
            )}
        />
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          type="edit"
          saveHandle={() => this.saveHandler(type)}
          render={({ show }) => {
            if (show) {
              return (
                  <div>
                    <h4 className="side-popup-title">
                      {titleSide}
                    </h4>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputText
                            label="Support Name"
                            placeholder="Support Name"
                            value={name}
                            changeEvent={(value, e) => this.handleChange(value, 'name', e)}
                            name="support_name"
                        />
                        <FieldFeedbacks for="support_name">
                          <FieldFeedback when={val => val === ''}>Support Name tidak boleh kosong</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputTextArea
                            label="Support Subtitle"
                            placeholder="Support Subtitle"
                            value={subtitle}
                            changeEvent={(value, e) => this.handleChange(value, 'subtitle', e)}
                            name="support_subtitle"
                        />
                        <FieldFeedbacks for="support_subtitle">
                          <FieldFeedback when={val => val === ''}>Support Subtitle tidak boleh kosong</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputTextArea
                            label="Support Detail"
                            placeholder="Support Detail"
                            value={desc}
                            changeEvent={(value, e) => this.handleChange(value, 'desc', e)}
                            name="support_detail"
                        />
                        <FieldFeedbacks for="support_detail">
                          <FieldFeedback when={val => val === ''}>Support Detail tidak boleh kosong</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputText
                            label="Support Unit"
                            placeholder="Support Unit"
                            value={unit}
                            changeEvent={(value, e) => this.handleChange(value, 'unit', e)}
                            name="support_unit"
                        />
                        <FieldFeedbacks for="support_unit">
                          <FieldFeedback when={val => val === ''}>Support Unit tidak boleh kosong</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputNumber
                            label="Support Price"
                            placeholder="Support Price"
                            value={harga}
                            changeEvent={(value, e) => this.handleChange(value, 'harga', e)}
                            name="support_price"
                        />
                        <FieldFeedbacks for="support_price">
                          <FieldFeedback when={val => val === ''}>Support Price tidak boleh kosong</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputText
                            label="Special Price"
                            placeholder="Special Price"
                            value={hargaSpesial}
                            changeEvent={value => this.setState({ hargaSpesial: value })}
                            name="special_price"
                        />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputNumber
                            label="Support Discount"
                            placeholder="Support Discount"
                            value={diskon}
                            changeEvent={(value, e) => this.handleChange(value, 'diskon', e)}
                            name="support_discount"
                        />
                        <FieldFeedbacks for="support_discount">
                          <FieldFeedback when={val => +val < 0 || +val > 100 || val === ''}>*filled discount coloum with range 0-100</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputNumber
                            label="Support Qty"
                            placeholder="Support Qty"
                            value={qty}
                            changeEvent={(value, e) => this.handleChange(value, 'qty', e)}
                            name="support_qty"
                        />
                        <FieldFeedbacks for="support_qty">
                          <FieldFeedback when={val => +val <= 0 || val === ''}>*Support Qty cannot be 0</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputNumber
                            label="Administration Fee"
                            placeholder="Administration Fee"
                            value={biayaTambahan}
                            changeEvent={value => this.setState({ biayaTambahan: value })}
                            name="administration_fee"
                        />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-6">
                        <InputNumber
                            label="Support Duration"
                            placeholder="Support Duration"
                            value={duration}
                            changeEvent={(value, e) => this.handleChange(value, 'duration', e)}
                            name="support_duration"
                        />
                        <FieldFeedbacks for="support_duration">
                          <FieldFeedback when={val => +val <= 0 || val === ''}>*Duration must be filled</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                      <div className="col-sm-6">
                        <InputSelect
                            data={SATUAN_DURATION}
                            value={satuan}
                            label="Satuan"
                            changeEvent={value => this.setState({ satuan: value })}
                            name="Satuan"
                        />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <Autocomplete
                            data={category}
                            selector="name"
                            value={categoryTerpilih}
                            changeEvent={value => this.handleChange(value, 'categoryTerpilih', { target: this.categoryName })}
                            placeholder="Search ..."
                            label="Category Name"
                            name="categoryTerpilih"
                        />
                        <input
                            className="hide-for-validation"
                            ref={(c) => { this.categoryName = c; }}
                            name="category_name"
                            type="text"
                            value={categoryTerpilih}
                            onChange={() => { }}
                        />
                        <FieldFeedbacks for="category_name">
                          <FieldFeedback when={() => categoryTerpilih === ''}>
                            <span className="text-danger">*Category Name must be filled</span>
                          </FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <Autocomplete
                            data={supportMasterActive}
                            selector="name"
                            value={supportMasterActiveTerpilih}
                            changeEvent={value => this.setState({ supportMasterActiveTerpilih: value })}
                            placeholder="Search ..."
                            label="Support Bonus (can be null)"
                            name="supportMasterActiveTerpilih"
                        />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputText
                            label="Additional QTY"
                            placeholder="Additional QTY"
                            value={additionalQty}
                            changeEvent={value => this.setState({ additionalQty: value })}
                            name="Additional_QTY"
                        />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputNumber
                            label="Seq"
                            placeholder="Seq"
                            value={seq}
                            changeEvent={value => this.setState({ seq: value })}
                            name="Seq"
                        />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-6 text-left">
                        <span className="control-label" style={{ paddingTop: '12px' }}>Active Status</span>
                      </div>
                      <div className="col-sm-6 text-right">
                        <Switch
                            className="text-right"
                            checked={status}
                            changeEvent={value => this.setState({ status: value })}
                            name="active_status"
                        />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-8 text-left">
                        <span className="control-label" style={{ paddingTop: '12px' }}>Is Need Location (Used when support need majoo team to come to user outlet)</span>
                      </div>
                      <div className="col-sm-4 text-right">
                        <Switch
                            className="text-right"
                            checked={isNeedLocation}
                            valueTrue="True"
                            valueFalse="False"
                            changeEvent={value => this.setState({ isNeedLocation: value })}
                            name="isNeedLocation"
                        />
                      </div>
                    </div>
                    <div className="row mb-sm">
                      <div className="col-sm-6 text-left">
                        <span className="control-label" style={{ paddingTop: '12px' }}>This support for sale</span>
                      </div>
                      <div className="col-sm-6 text-right">
                        <Switch
                            className="text-right"
                            checked={isForSale}
                            valueTrue="True"
                            valueFalse="False"
                            changeEvent={value => this.setState({ isForSale: value })}
                            name="isForSale"
                        />
                      </div>
                    </div>
                  </div>
              );
            }
            return (null);
          }
        }
        />
        <DeleteConfirm
          title="Hapus Tittle"
          confirmText="Ya, Hapus"
          cancelText="Tidak, Simpan"
          confirmHandle={() => { this.removeHandler(); }}
          ref={(c) => { this.deleteConfirm = c; }}
        >
          Menghapus pembelian akan menghilangkan secara permanen
          dari daftar Produk anda. Tindakan ini tidak bisa dibatalkan
        </DeleteConfirm>

        <ModalPopup
          src={imgSrc}
          name={captionGambar}
          clickEvent={() => { this.removeModal(); }}
          displayed={displayed}
        />
      </div>
    );
  }
}

export default CoreHOC(Supportmaster);

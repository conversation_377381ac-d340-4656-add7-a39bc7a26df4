import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CoreHOC from '../../../core/CoreHOC';

/* COMPONENTS */
import Table from '../../../components/table/v.2/Table';
import Switch from '../../../components/form/Switch';
import SidePopup from '../../../components/sidepopup/Container';
import InputNumber from '../../../components/form/InputNumber';
import Autocomplete from '../../../components/form/Autocomplete';
import SelectMultiple from '../../../components/form/SelectMultiple';

/* DATA */
import {
  getSupportLocation, updateSupportLocation, addSupportLocation, getSupportNeedLocation,
} from '../../../data/sales/master';
import { getUserWilayah } from '../../../data';

/* CONFIG */
import { TABLE_META, ERROR_MESSAGE } from './config/SupportLocation';

class Pembelian extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.node,
    router: PropTypes.node,
    assignFilterColoumn: PropTypes.func,
		assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    assignFilterColoumn: () => { },
		assignRangeDate: () => { },
    notificationSystem: null,
    router: null,
  }

  constructor(props) {
    super(props);

    this.state = {
      supportListLocation: [],
      listNegara: [],
      listProvinsi: [],
      listKota: [],
      titleSide: '',
      price: '',
      negara: [],
      provinsi: [],
      kota: [],
      supportList: [],
      support: '',
      id: '',
      type: 'add',
      active: false,
      msgNegara: '',
      msgProvinsi: '',
      msgKota: '',
      msgSupport: '',
      msgPrice: '',

    };
  }

  componentWillMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons, router, notificationSystem,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([{ type: 'primary', content: <span> Add Support Location </span>, action: () => { this.callAddHandler(); } }]);
    this.getNewData();
    getUserWilayah().then((response) => {
      const listNegara = [];
      response.data.forEach((data) => {
        const kirim = {
          label: data.name,
          value: data.id,
          provinsi: data.province,
        };
        listNegara.push(kirim);
      });
      this.setState({
        listNegara,
      });
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Get data failed',
          message: '',
          level: 'error',
        });
      }
    });

    getSupportNeedLocation().then((response) => {
      this.setState({
        supportList: response.data,
      });
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Get data failed',
          message: '',
          level: 'error',
        });
      }
    });
  }

  getNewData() {
    const { router, notificationSystem } = this.props;
    getSupportLocation().then((response) => {
      this.setState({
        supportListLocation: response.data,
      });
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Get data failed',
          message: '',
          level: 'error',
        });
      }
    });
  }

  callEditDetailHandler({ _original: value }) {
    const { listNegara } = this.state;
    let listProvinsi = [];
    let listKota = [];
    value.negara.forEach((n) => {
      listNegara.forEach(() => {
        const cari = listNegara.filter(d => d.value === n);
        if (cari.length > 0) {
          cari[0].provinsi.forEach((c) => {
            const kirim = {
              value: c.id,
              label: c.name,
              kota: c.city,
            };
            listProvinsi = [...listProvinsi, kirim];
          });
        }
      });
    });
    value.provinsi.forEach((vp) => {
      const cari = listProvinsi.filter(d => d.value === vp);
      if (cari.length > 0) {
        cari[0].kota.forEach((k) => {
          const kirim = {
            value: k.id,
            label: k.name,
            kota: k.kota,
          };
          listKota = [...listKota, kirim];
        });
      }
    });

    this.setState({
      kota: value.kota,
      provinsi: value.provinsi,
      negara: value.negara,
      active: Number(value.active) === 1,
      support: value.id_support,
      price: value.harga,
      type: 'update',
      id: value.id,
      listProvinsi,
      listKota,
    }, () => {
      this.clearMsgError();
      this.sidePop.showPopup();
    });
  }

  callAddHandler() {
    this.setState({
      negara: [],
      provinsi: [],
      kota: [],
      price: '',
      support: '',
      type: 'add',
      id: '',
      active: true,
    }, () => {
      this.clearMsgError();
      this.sidePop.showPopup();
    });
  }

  saveHandler(type) {
    const {
      id,
      negara,
      provinsi,
      kota,
      price,
      active,
      support,
    } = this.state;
    const param = {
      id,
      negara,
      provinsi,
      kota,
      price,
      active,
      support,
    };
    const { router, notificationSystem } = this.props;

    let error = false;

    if (negara.length === 0) {
      this.setState({
        msgNegara: ERROR_MESSAGE.NEGARA,
      });
      error = true;
    }
    if (provinsi.length === 0) {
      this.setState({
        msgProvinsi: ERROR_MESSAGE.PROVINSI,
      });
      error = true;
    }
    if (kota.length === 0) {
      this.setState({
        msgKota: ERROR_MESSAGE.KOTA,
      });
      error = true;
    }
    if (support === '') {
      this.setState({
        msgSupport: ERROR_MESSAGE.SUPPORT,
      });
      error = true;
    }
    if (price === '') {
      this.setState({
        msgPrice: ERROR_MESSAGE.PRICE,
      });
      error = true;
    }
    if (error) {
      return;
    }

    if (type === 'add') {
      addSupportLocation(param).then(() => {
        notificationSystem.addNotification({
          title: 'Add success',
          // message: '': '',
          level: 'success',
        });
        this.getNewData();
        this.sidePop.hidePopup();
      }, (message) => {
        if (!message) {
          router.push('/auth/login');
        } else {
          notificationSystem.addNotification({
            title: 'Get data failed',
            message: '',
            level: 'error',
          });
        }
      });
    } else {
      updateSupportLocation(param).then(() => {
        notificationSystem.addNotification({
          title: 'Update success',
          // message: '': '',
          level: 'success',
        });
        this.getNewData();
        this.sidePop.hidePopup();
      }, (message) => {
        if (!message) {
          router.push('/auth/login');
        } else {
          notificationSystem.addNotification({
            title: 'Get data failed',
            message: '',
            level: 'error',
          });
        }
      });
    }
  }


  changeEventNegara(value) {
    const { listNegara } = this.state;
    let provinceOptions = [];
    const negara = [];
    let provinceOptionsFix = [];
    value.forEach((dat) => {
      negara.push(dat.value);
      const cari = listNegara.filter(d => d.value === dat.value);
      if (cari.length > 0) {
        provinceOptions = [...provinceOptions, cari[0].provinsi];
      }
    });
    provinceOptions.forEach((p) => {
      p.forEach((px) => {
        const kirim = {
          value: px.id,
          label: px.name,
          kota: px.city,
        };
        provinceOptionsFix = [...provinceOptionsFix, kirim];
      });
    });
    this.setState({
      listProvinsi: provinceOptionsFix,
      provinsi: [],
      kota: [],
      listKota: [],
      negara,
      msgNegara: negara.length === 0 ? ERROR_MESSAGE.NEGARA : '',
    });
  }

  clearMsgError() {
    this.setState({
      msgNegara: '',
      msgProvinsi: '',
      msgKota: '',
      msgSupport: '',
      msgPrice: '',
    });
  }

  changeEventProvinsi(value) {
    const { listProvinsi, kota } = this.state;
    let kotaOption = [];
    const provinsi = [];
    let kotaOptionFix = [];
    let newKota = [];

    value.forEach((dat) => {
      provinsi.push(dat.value);
      const cari = listProvinsi.filter(d => d.value === dat.value);
      if (cari.length > 0) {
        kotaOption = [...kotaOption, cari[0].kota];
      }
    });
    kotaOption.forEach((p) => {
      p.forEach((px) => {
        const kirim = {
          value: px.id,
          label: px.name,
          kota: px.kota,
        };
        kotaOptionFix = [...kotaOptionFix, kirim];
        newKota = [...newKota, ...kota.filter(item => item.value === px.id || item === px.id)];
      });
    });
    this.setState({
      listKota: kotaOptionFix,
      kota: newKota,
      provinsi,
      msgProvinsi: provinsi.length === 0 ? ERROR_MESSAGE.PROVINSI : '',
    });
  }

  changeEventKota(value) {
    this.setState({
      kota: value,
      msgKota: value.length === 0 ? ERROR_MESSAGE.KOTA : '',
    });
  }

  changeEventSupport(value) {
    this.setState({
      support: value,
      msgSupport: value === '' ? ERROR_MESSAGE.SUPPORT : '',
    });
  }

  changeEventPrice(value) {
    this.setState({
      price: value,
      msgPrice: value === '' ? ERROR_MESSAGE.PRICE : '',
    });
  }

  render() {
    const {
      supportListLocation, titleSide, type, listNegara, negara, listProvinsi, provinsi, listKota, kota, supportList, support, price, active, msgNegara, msgProvinsi, msgKota, msgSupport, msgPrice,
    } = this.state;
    return (
      <div>
        <Table
          ref={(c) => {
              this.table = c;
          }}
          columns={TABLE_META}
          content={supportListLocation}
          rowEvent={val => this.callEditDetailHandler(val)}
          withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
              <section className="panel">
                  <div className="panel-heading table-header">
                      <div className="row">
                          <div className="col-md-8">
                              <h4 className="panel-title" style={{ margin: '8px' }}>
                                Pembelian
                              </h4>
                          </div>
                          <div className="col-md-3">
                              <InputSearch />
                          </div>
                          <div className="col-md-1">
                              <PageSize />
                          </div>
                      </div>
                  </div>
                  <div className="panel-body">
                      {makeTable()}
                  </div>
              </section>
          )}
        />
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          type="edit"
          saveHandle={() => this.saveHandler(type)}
        >
          <h4 className="side-popup-title">
            {titleSide}
          </h4>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <SelectMultiple
                  options={listNegara}
                  selector="name"
                  value={negara}
                  changeEvent={(val) => { this.changeEventNegara(val); }}
                  placeholder="Search ..."
                  label="Country"
                />
                {msgNegara !== '' && <font color="red" style={{ fontSize: 12, fontWeight: 'bold' }}>{msgNegara}</font>}
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <SelectMultiple
                  options={listProvinsi}
                  selector="name"
                  value={provinsi}
                  changeEvent={(val) => { this.changeEventProvinsi(val); }}
                  placeholder="Search ..."
                  label="Province"
                />
                {msgProvinsi !== '' && <font color="red" style={{ fontSize: 12, fontWeight: 'bold' }}>{msgProvinsi}</font>}
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <SelectMultiple
                  options={listKota}
                  selector="name"
                  value={kota}
                  changeEvent={(val) => { this.changeEventKota(val); }}
                  placeholder="Search ..."
                  label="City"
                />
                {msgKota !== '' && <font color="red" style={{ fontSize: 12, fontWeight: 'bold' }}>{msgKota}</font>}
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <Autocomplete
                  data={supportList}
                  selector="name"
                  value={support}
                  changeEvent={value => this.changeEventSupport(value)}
                  placeholder="Search ..."
                  label="Support Name"
                />
                {msgSupport !== '' && <font color="red" style={{ fontSize: 12, fontWeight: 'bold' }}>{msgSupport}</font>}
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputNumber label="Acomodation Price" placeholder="Acomodation Price" value={price} changeEvent={value => this.changeEventPrice(value)} />
                {msgPrice !== '' && <font color="red" style={{ fontSize: 12, fontWeight: 'bold' }}>{msgPrice}</font>}
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <Switch className="text-right" checked={active} changeEvent={value => this.setState({ active: value })} />
              </div>
            </div>
        </SidePopup>
      </div>
    );
  }
}

export default CoreHOC(Pembelian);

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';

import CoreHOC from '../../../core/CoreHOC';

import SidePopup from '../../../components/sidepopup/ContainerV2';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';

import BuySupportInternal from './Sidebar/v2';
import DetailSidebar from './Sidebar/DetailSidebar';

import List from './PurchaseList';

import {
  updatePenjualan, deletePenjualan, getDetailPro,
} from '../../../data/sales/support';

import { FORM_TYPES } from '../../../enum/form';
import { IS_PRO } from './config/Pembelian';
import { catchError } from '../../../utils/helper';
import { getSupportCategoryActive } from '../../../data/sales/support';

class Pembelian extends Component {
  constructor(props) {
    super(props);

    this.state = {
      isSpecial: 'Ya',
      exp: '',
      isPro: '',
      isDisable: false,
      statusPerubahan: false,
      disableharga: false,
      onlinePayment: false,
      statusList: [],
      imgList: [],
      listFiltered: [],
      listDetailPro: [],
      listCategorySupport: [],
      historyLists: [],
      form: {
        idPembelian: '',
        jenis: '',
        qty: '',
        user: '',
        name: '',
        emailOwner: '',
        email: '',
        telepon: '',
        cabang: '',
        provinsi: '',
        kota: '',
        alamat: '',
        biayaUpgrade: 0,
        biayaAdministrasi: '',
        discount: 0,
        supportBill: 0,
        biayaAkomodasi: 0,
        transactionStatus: '',
        paymentType: '',
        idStatus: '',
        note: '',
        statusOnlinePayment: '',
        boughtBy: '',
        pengubah: '',
        isAutoRenewal: null,
      },
    };
  }

  componentDidMount = () => {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([{ type: 'primary', content: <span> Add Buy Support </span>, action: () => { this.callManageHandler(); } }]);
    this.getSupport();
  }

  componentWillReceiveProps = () => {
    const { location } = this.props;
    const param = location.search;

    if (param !== null || param !== undefined) {
      const pecah = param.split('=');
      const id = pecah[1];
      const { listFiltered } = this.state;
      const data = listFiltered.filter(d => d.id_pembelian === id);
      if (data.length > 0) {
        this.callEditDetailHandler(data[0]);
      }
    }
  }

  componentWillUpdate = (nextProps, nextState) => {
    const { statusOnlinePayment } = this.state;

    if (statusOnlinePayment !== nextState.statusOnlinePayment) {
      this.changeStatusOnlinePayment(nextState);
    }
  }

  changeDateHandler = (startDate, endDate) => {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);
  }

  changeStatusOnlinePayment = ({ statusOnlinePayment }) => {
    const { form } = this.state;

    const newForm = update(form, {
      statusOnlinePayment: { $set: statusOnlinePayment },
    });

    this.setState({
      form: newForm,
    });
  }

  callEditDetailHandler = async ({ _original: value }) => {
    const { form } = this.state;
    const { showProgress, hideProgress, notificationSystem } = this.props;

    const {
      is_special: newIsSpecial, payment_type: paymentType, gambar, statusPerubahan, id_status: idStatus,
      online_payment: mewOnlinePayment, is_pro: isPro, id_pembelian: idPembelian,
      name, jenis, qty, user, user_email: userEmail, email, telepon,
      cabang, provinsi_name: provinsiName, kota_name: kotaName, alamat,
      administration, discount, support_bill: supportBill, acomodation,
      transaction_status: transactionStatus, note, bought_by: boughtBy,
      pengubah, is_auto_renewal: isAutoRenewal,
    } = value;

    const isSpecial = newIsSpecial === 'Ya' ? 'Bill (Is Special Price)' : 'Bill';
    let biayaUpgrade = 0;
    let isDisable = false;
    let disableharga = false;
    let onlinePayment = false;

    if (paymentType !== 'Manual') {
      onlinePayment = true;
    }

    const gambarArray = (gambar || []).map((x, idx) => ({
      src: gambar[idx], name: `${name}${idx}`,
    }));

    let statusPer = false;
    if (
      statusPerubahan === 'benar'
      && idStatus !== '4'
      && idStatus !== '35'
      && (mewOnlinePayment === null || mewOnlinePayment === '')
    ) {
      statusPer = true;
    }

    if (
      idStatus === 35 || idStatus === 4
    ) {
      isDisable = true;
    }

    if (isPro === IS_PRO.YES) {
      disableharga = true;

      try {
        showProgress();
        const res = await getDetailPro({ id: idPembelian });
        const {
          data: { list: listDetailPro, history_transaction: historyLists }, upgrade,
        } = res;

        biayaUpgrade = upgrade;

        this.setState({
          listDetailPro,
          historyLists,
        });
      } catch (e) {
        notificationSystem.addNotification({
          title: 'Failed',
          message: catchError(e),
          level: 'error',
        });
      } finally {
        hideProgress();
      }
    }

    const newForm = update(form, {
      idPembelian: { $set: idPembelian },
      jenis: { $set: jenis },
      qty: { $set: qty > 90000 ? '~' : qty },
      user: { $set: user },
      name: { $set: name },
      emailOwner: { $set: userEmail },
      email: { $set: email },
      telepon: { $set: telepon },
      cabang: { $set: cabang },
      provinsi: { $set: provinsiName },
      kota: { $set: kotaName },
      alamat: { $set: alamat },
      biayaUpgrade: { $set: biayaUpgrade },
      biayaAdministrasi: { $set: administration },
      discount: { $set: discount },
      supportBill: { $set: supportBill },
      biayaAkomodasi: { $set: acomodation },
      transactionStatus: { $set: transactionStatus },
      paymentType: { $set: paymentType },
      idStatus: { $set: idStatus },
      note: { $set: note || '' },
      boughtBy: { $set: boughtBy },
      pengubah: { $set: pengubah || '' },
      isAutoRenewal: { $set: isAutoRenewal },
    });

    this.setState({
      form: newForm,
      statusPerubahan: statusPer,
      imgList: gambarArray,
      exp: value.exp,
      isSpecial,
      isDisable,
      isPro: value.is_pro,
      disableharga,
      onlinePayment,
    }, () => {
      this.sidePop.showPopup();
    });
  }

  saveHandler = async (saveType, errorCallback) => {
    const { showProgress, hideProgress, notificationSystem } = this.props;
    const {
      isPro, exp,
      form: {
        idPembelian, biayaAkomodasi, biayaAdministrasi,
        supportBill, idStatus,
      },
    } = this.state;

    showProgress();
    try {
      const payload = {
        status_id: Number(idStatus),
        transaction_id: idPembelian,
        item_price: Number(supportBill),
        exp,
        is_premium: isPro,
        additional_price: biayaAdministrasi,
        accommodation_fee: biayaAkomodasi,
      };

      const res = await updatePenjualan(payload);
      if (!res.status) throw new Error(res);

      this.sidePop.hidePopup();
      notificationSystem.addNotification({
        title: 'Success',
        message: 'Update support purchase success',
        level: 'success',
      });
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Failed',
        message: catchError(e),
        level: 'error',
      });
      errorCallback();
    } finally {
      this.recall();
      hideProgress();
    }
  }

  recall = () => { this.purchaseList.refetchTable(); }

  // callRemoveHandler = () => {
  //   this.deleteConfirm.showPopup();
  //   this.sidePop.hidePopup();
  // }

  // removeHandler = async () => {
  //   const { form: { idPembelian } } = this.state;
  //   const { notificationSystem } = this.props;

  //   const parameter = {
  //     penyimpanan: 9,
  //     id_pembelian: idPembelian,
  //   };

  //   try {
  //     await deletePenjualan(parameter);
  //     notificationSystem.addNotification({
  //       title: 'Success',
  //       message: 'Delete support purchase success',
  //       level: 'success',
  //     });
  //     this.recall();
  //   } catch (e) {
  //     notificationSystem.addNotification({
  //       title: 'Failed',
  //       message: catchError(e),
  //       level: 'error',
  //     });
  //   }
  // }

  callManageHandler = () => {
    this.sidePopAdd.showPopup();
  }

  changeEvent = (key, value) => new Promise((resolve) => {
    const { form } = this.state;

    const newForm = update(form, {
        [key]: { $set: value },
    });

    this.setState({
        form: newForm,
    }, () => {
        resolve();
    });
  })

  getSupport = async () => {
    const { notificationSystem } = this.props;

    try {
      const res = await getSupportCategoryActive();
      const { data: listCategorySupport } = res;

      this.setState({
          listCategorySupport,
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data User',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  render() {
    const {
      form, listDetailPro, imgList, isPro, disableharga, isSpecial, onlinePayment,
      statusPerubahan, statusList, isDisable, listCategorySupport, historyLists,
    } = this.state;

    const {
      notificationSystem, showProgress, hideProgress,
    } = this.props;

    return (
      <div>
        <List
          {...this.props}
          ref={(c) => { this.purchaseList = c; }}
          onRowClick={this.callEditDetailHandler}
          handleUpdateStatusList={val => this.setState({ statusList: val })}
          statusList={statusList}
          listCategorySupport={listCategorySupport}
        />

        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={900}
          type={FORM_TYPES.EDIT}
          btnSaveDisabled={isDisable}
          saveHandle={(statusPerubahan) && this.saveHandler}
          // removeHandle={(statusPerubahan) && this.callRemoveHandler}
          render={({ validateInput }) => (
            <DetailSidebar
              data={form}
              listDetailPro={listDetailPro}
              historyLists={historyLists}
              statusList={statusList}
              imgList={imgList}
              isPro={isPro}
              statusPerubahan={statusPerubahan}
              disableharga={disableharga}
              isSpecial={isSpecial}
              onlinePayment={onlinePayment}
              changeEvent={this.changeEvent}
              validateInput={validateInput}
            />
          )}
        />

        <BuySupportInternal
          listCategorySupport={listCategorySupport}
          success={() => { this.recall(); }}
          ref={(c) => { this.sidePopAdd = c; }}
          notificationSystem={notificationSystem}
          showProgress={showProgress}
          hideProgress={hideProgress}
        />

        {/* <DeleteConfirm
          title="Delete Data"
          confirmText="Yes, Delete"
          cancelText="No, Save"
          confirmHandle={() => { this.removeHandler(); }}
          ref={(c) => { this.deleteConfirm = c; }}
        >
          Delete payment purchase will be remove this from your data.
          This action can not be canceled
        </DeleteConfirm> */}
      </div>
    );
  }
}

Pembelian.propTypes = {
  calendar: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
  }),
  assignCalendar: PropTypes.func,
  assignButtons: PropTypes.func,
  notificationSystem: PropTypes.shape({
    addNotification: PropTypes.func,
  }),
  location: PropTypes.shape({
    search: PropTypes.string,
  }),
  assignFilterColoumn: PropTypes.func,
  assignRangeDate: PropTypes.func,
  showProgress: PropTypes.func.isRequired,
  hideProgress: PropTypes.func.isRequired,
};

Pembelian.defaultProps = {
  calendar: {
    start: '',
    end: '',
  },
  location: ({
    search: '',
  }),
  assignCalendar: () => { },
  assignButtons: () => { },
  assignFilterColoumn: () => { },
  assignRangeDate: () => { },
  notificationSystem: ({
    addNotification: () => {},
  }),
};

export default CoreHOC(Pembelian);

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/table/v.2/Table';
import SidePopup from '../../../components/sidepopup/Container';
import InputText from '../../../components/form/InputText';
import InputNumber from '../../../components/form/InputNumber';
import InputTextArea from '../../../components/form/InputTextArea';
import InputSelect from '../../../components/form/InputSelect';
import Select from '../../../components/form/Select';
import DeleteConfirm from '../../../components/modalpopup/DeleteConfirm';
import SaveConfirm from '../../../components/modalpopup/SaveConfirm';

import { getClaim, updateClaim, deleteClaim } from '../../../data/sales/support';
import { TABLE_META } from './config/Claim';

class Claim extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.node,
    router: PropTypes.node,
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    assignCalendar: () => { },
    assignButtons: () => { },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
    notificationSystem: null,
    router: null,
  }

  constructor(props) {
    super(props);

    this.state = {
      claimList: [],
      claimListFiltered: [],
      jenis: [],
      jenisEdit: [],
      jenisTerpilih: '0',
      tanggalStart: '',
      tanggalEnd: '',
      jenisSupport: '',
      detailSupport: '',
      qty: '',
      user: '',
      pemesan: '',
      email: '',
      telepon: '',
      outlet: '',
      alamat: '',
      kode: '',
      catatan: '',
      pengubah: '',
      id_update: '',
      waktu_claim: '',
      statusPerubahan: false,
      id_user_support: '',
      isDisable: false,
      jenisStatus: 'cancel',
      continoueProccess: true,
      userNote: '',
    };
  }

  componentWillMount() {
    const {
      assignCalendar, assignButtons, assignFilterColoumn, assignRangeDate,
      calendar,
    } = this.props;
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([]);
    assignFilterColoumn([]);
    assignRangeDate([]);
    const tanggalStart = calendar.start;
    const tanggalEnd = calendar.end;
    this.setState({
      tanggalStart,
      tanggalEnd,
    }, () => {
      this.getData();
    });
  }

  getData() {
    const { tanggalStart, tanggalEnd, jenisTerpilih } = this.state;
    const { router, notificationSystem } = this.props;
    const tglMulai = tanggalStart.split('/');
    const tglAkhir = tanggalEnd.split('/');
    const param = {
      start: `${tglMulai[2]}-${tglMulai[1]}-${tglMulai[0]}`,
      end: `${tglAkhir[2]}-${tglAkhir[1]}-${tglAkhir[0]}`,
    };
    getClaim(param).then((responseClaim) => {
      this.sidePop.hidePopup();
      const i = [];
      const k = [];
      responseClaim.data.status.forEach((x) => {
        const j = [x.id, x.label];
        if (x.id !== 0) {
          k.push(j);
        }
        i.push(j);
      });

      this.setState({
        claimList: responseClaim.data.summary,
        jenis: i,
        jenisEdit: k,
        tanggalStart,
        tanggalEnd,
      }, () => { this.changeCustomJenis(jenisTerpilih); });
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Get data success',
          message: '',
          level: 'error',
        });
      }
    });
  }

  callEditDetailHandler(props) {
    const value = props._original;
    let isDisable = false;
    let statusPer = false;

    if (value.statusPerubahan === 'benar') {
      statusPer = true;
    }

    if (value.id_status === 37) {
      isDisable = true;
    }

    this.setState({
      jenisSupport: value.jenis,
      detailSupport: value.detail_support,
      qty: value.qty,
      user: value.user,
      pemesan: value.nama,
      email: value.email,
      telepon: value.telepon,
      outlet: value.outlet,
      alamat: value.alamat,
      kode: value.claim_ticket,
      catatan: value.catatan || '',
      pengubah: value.pengubah,
      id_status: value.id_status,
      id_update: value.id_claim,
      waktu_claim: value.waktu_claim,
      statusPerubahan: statusPer,
      id_user_support: value.id_user_support,
      isDisable,
      continoueProccess: true,
      userNote: value.user_note || '',
    }, () => {
      this.sidePop.showPopup();
    });
  }

  saveHandler() {
    const { continoueProccess: continoueProccessRaw, id_status: idStatus } = this.state;
    let continoueProccess = continoueProccessRaw;
    if (idStatus === '4' || idStatus === '37') {
      this.setState({
        jenisStatus: idStatus === '4' ? 'Cancel' : 'Close',
      }, () => {
        this.saveConfirmStatus.showPopup();
      });
      continoueProccess = false;
    }
    if (!continoueProccess) {
      return;
    }
    this.exeSave();
  }

  exeSave() {
    const {
      notificationSystem, router,
    } = this.props;
    const {
      pemesan, email, catatan, telepon, alamat, id_status: status, id_update: id,
      jenisSupport, detailSupport, kode, outlet, nama, waktu_claim: waktu,
      id_user_support: userSuport, qty,
    } = this.state;
    const parameter = {
      pemesan,
      email,
      catatan,
      telepon,
      alamat,
      status,
      id,
      jenis_support: jenisSupport,
      detail_support: detailSupport,
      kode,
      outlet,
      nama,
      waktu,
      id_user_support: userSuport,
      qty,
    };

    updateClaim(parameter).then(() => {
      notificationSystem.addNotification({
        title: 'Berhasil',
        message: 'Update claim success',
        level: 'success',
      });
      this.setState({
        jenisStatus: '',
      });
      this.getData();
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Update claim failed',
          message: '',
          level: 'error',
        });
      }
    });
  }

  callRemoveHandler() {
    this.sidePop.hidePopup();
    this.deleteConfirm.showPopup();
  }

  removeHandler() {
    const {
      notificationSystem, router,
    } = this.props;
    const { id_update: id } = this.state;
    const parameter = {
      id,
      status: 9,
    };

    deleteClaim(parameter).then(() => {
      notificationSystem.addNotification({
        title: 'Berhasil',
        message: 'Update claim success',
        level: 'success',
      });
      this.getData();
    }, (message) => {
      if (!message) {
        router.push('/auth/login');
      } else {
        notificationSystem.addNotification({
          title: 'Update claim failed',
          message: '',
          level: 'error',
        });
      }
    });
  }

  changeDateHandler(startDate, endDate) {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);
    this.setState({
      tanggalStart: startDate,
      tanggalEnd: endDate,
    }, () => {
      this.getData();
    });
  }

  changeEventStatus(idStatus) {
    this.setState({
      id_status: idStatus,
    });
  }


  changeCustomJenis(jenisTerpilih) {
    const { claimList } = this.state;
    this.setState({
      jenisTerpilih,
      claimListFiltered: [],
    }, () => {
      if (jenisTerpilih === 0 || jenisTerpilih === '0') {
        this.setState({
          claimListFiltered: claimList,
        });
      } else {
        const filteredData = claimList.filter(d => d.id_status === jenisTerpilih);
        this.setState({
          claimListFiltered: filteredData,
        });
      }
    });
  }

  confirmHandler() {
    this.setState({
      continoueProccess: true,
    }, () => {
      this.saveConfirmStatus.hidePopup();
      this.exeSave();
    });
  }

  render() {
    const {
      claimListFiltered, jenis, jenisTerpilih, isDisable,
      pemesan, email, catatan, telepon, alamat, jenis_support: jenisSupport,
      kode, outlet, qty, user, jenisEdit, id_status: idStatus, statusPerubahan,
      userNote, pengubah, jenisStatus,
    } = this.state;
    return (
      <div>
        <Table
            columns={TABLE_META}
            content={claimListFiltered}
            rowEvent={data => this.callEditDetailHandler(data)}
            withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
                <section className="panel">
                  <div className="panel-heading">
                    <h4 className="panel-title">Claim</h4>
                  </div>
                  <div className="panel-heading table-header">
                    <div className="row">
                      <div className="col-md-12 row" style={{ width: '100%' }}>
                        <div className="col-md-2" style={{ paddingRight: '0px', float: 'right' }}>
                          <PageSize />
                        </div>
                        <div className="col-md-2" style={{ paddingRight: '0px', float: 'right' }}>
                          <InputSearch />
                        </div>
                        <div className="col-md-2" style={{ paddingRight: '0px', float: 'right' }}>
                          <Select
                              data={jenis}
                              value={jenisTerpilih}
                              changeEvent={val => this.changeCustomJenis(val)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="panel-body">
                    {makeTable()}
                  </div>
                </section>
            )}
        />
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          type="edit"
          isDisable={isDisable}
          saveHandle={() => this.saveHandler()}
          removeHandle={() => this.callRemoveHandler()}
        >
          <h4 className="side-popup-title">
            Claim Support
          </h4>
            <div className="row mb-sm">
              <div className="col-sm-6">
                <InputText label="Support Type" placeholder="Support Type" value={jenisSupport} disabled />
              </div>
              <div className="col-sm-6">
                <InputNumber label="Qty" placeholder="Qty" value={qty} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputText label="User" placeholder="User" value={user} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputText label="Buyer" placeholder="Buyer" value={pemesan} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputText label="Email" placeholder="Email" value={email} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputText label="Phone" placeholder="Phone" value={telepon} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputText label="Outlet" placeholder="Outlet" value={outlet} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputTextArea label="Address" placeholder="Address" disabled value={alamat} />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputText label="Code" placeholder="Code" value={kode} disabled />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputSelect
                  data={jenisEdit}
                  value={idStatus}
                  disabled={statusPerubahan}
                  label="Status"
                  changeEvent={(val) => { this.changeEventStatus(val); }}
                />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputTextArea label="Note" placeholder="Note" value={catatan} disabled={statusPerubahan} changeEvent={value => this.setState({ catatan: value })} />
              </div>
            </div>
            <div className="row mb-sm">
              <div className="col-sm-12">
                <InputTextArea label="User Note" placeholder="Note" value={userNote} disabled />
              </div>
            </div>
            {pengubah !== '' && pengubah !== null
              && (
                <div className="row mb-sm">
                  <div className="col-sm-12">
                    <InputText label="Updated By" placeholder="" value={pengubah} disabled />
                  </div>
                </div>
            )}
        </SidePopup>
        <DeleteConfirm
          title="Remove Claim List"
          confirmText="Yes, Please"
          cancelText="No"
          confirmHandle={() => { this.removeHandler(); }}
          ref={(c) => { this.deleteConfirm = c; }}
        >
          {`${kode} will be removed from claim List.`}
        </DeleteConfirm>
        <SaveConfirm
          headerTitle="Change Status"
          confirmHandle={() => { this.confirmHandler(); }}
          confirmMsg={`Are you sure to ${jenisStatus} this ?`}
          confirmText="Yes, Please"
          cancelText="No"
          ref={(c) => { this.saveConfirmStatus = c; }}
        >
          {`Are you sure to ${jenisStatus} this ?` }
        </SaveConfirm>
      </div>
    );
  }
}

export default CoreHOC(Claim);

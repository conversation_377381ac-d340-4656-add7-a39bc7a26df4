import React, { Component } from 'react';
import PropTypes from 'prop-types';
import * as moment from 'moment';
import CoreHOC from '../../../core/CoreHOC';
import { catchError } from '../../../utils/helper';

/* COMPONENTS */
import SwitchBox from '../../../components/form/SwitchBox';
import Button from '../../../components/form/Button';
import Select from '../../../components/form/Select';
import Table, { getFilterValue, getUpdatedFilterValue } from '../../../components/table/v.2/Table';
import CalendarPick from '../../../components/form/CalendarPick';
import addCheckboxInTable from '../../../components/table/v.2/HoC/addCheckboxInTable';

/* DATA */
import { getPost, getContentCategory, updatePostBulk } from '../../../data/setting/portal';

/* CONFIG */
import {
  TABLE_META, STATUS_LIST, SWITCH_LIST, FILTER_TYPE, STATUS_POST_ENUM, STATUS_ENUM as statusSelectEnum,
} from './config/Solusi';
import {
  POST_TYPE_ENUM as postType,
} from './config/Post';

const TableWithCheckbox = addCheckboxInTable(Table);
@CoreHOC
export default class Solusi extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }).isRequired,
    assignCalendar: PropTypes.func.isRequired,
    assignButtons: PropTypes.func.isRequired,
    router: PropTypes.shape({
      push: PropTypes.func,
    }).isRequired,
    assignFilterColoumn: PropTypes.func.isRequired,
    assignRangeDate: PropTypes.func.isRequired,
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
  }

  static defaultProps = {
    notificationSystem: ({
      addNotification: () => {},
    }),
  }

  constructor(props) {
    super(props);

    this.state = {
      type: postType.SOLUSI,
      category: [],
      statusSelect: '',
      filterTable: [
        { id: FILTER_TYPE.DATE_RANGE, value: { start: '', end: '' } },
        { id: FILTER_TYPE.STATUS, value: 'all' },
        { id: FILTER_TYPE.CATEGORY, value: 0 },
      ],
    };
  }

  componentDidMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([
      { type: 'primary', content: <span> Add New Post </span>, action: () => { this.callManageHandler(); } },
    ]);
    this.getCategory();
  }

  getCategory = async () => {
    const { type } = this.state;
    const res = await this.fetcher(getContentCategory, { type });
    if (res !== null) {
      this.setState({
        category: [{ id: 0, value: 'All Category' }, ...res.data],
      });
    }
  }

  fetcher = async (fetchApi, params) => {
    try {
      const response = await fetchApi(params);
      if ('status' in response) {
        if (response.status) {
          return response;
        }

        if (!response.status) {
          let errMsg;

          if ('msg' in response) errMsg = response.msg;

          throw new Error(errMsg);
        }
      }
    } catch (e) {
      const { notificationSystem } = this.props;
      notificationSystem.addNotification({
        title: 'Terjadi kesalahan',
        message: catchError(e),
        level: 'error',
      });
    }

    return null;
  }

  _onFetch = async (state) => {
    const { notificationSystem } = this.props;
    const {
      page, pages, sorted, filtered, pageSize,
    } = state;
    const { type } = this.state;
    const { length } = sorted;

    const { start, end } = getFilterValue(filtered, FILTER_TYPE.DATE_RANGE);
    const statusFilter = getFilterValue(filtered, FILTER_TYPE.STATUS);
    const categoryFilter = getFilterValue(filtered, FILTER_TYPE.CATEGORY);

    let payload = {
      ...start && { startDate: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD') },
      ...end && { endDate: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD') },
      status: statusFilter,
      filterByIdCategory: categoryFilter,
      resultPerpage: pageSize,
      page: Number(page) + 1,
      filterByType: type,
    };

    // get sort params
    if (length > 0) {
      const { [length - 1]: { id, desc } } = sorted;
      payload = { ...payload, ...{ column: id, isAsc: desc ? 'DESC' : 'ASC' } };
    }

    // get filter params
    if (filtered && filtered.length > 0) {
      const filterAll = filtered.find(data => data.id === 'all');
      if (filterAll && filterAll.value !== '') {
        payload = {
          ...payload,
          keyword: filterAll.value,
        };
      }
    }

    let err = null,
    retval = {
      data: [],
      pageCount: pages > -1 ? pages : -1,
    };

    try {
      const { data, total } = await getPost(payload);
      const pageCount = Math.ceil(Number(total) / Number(pageSize));
      retval = { data, pageCount };
    } catch (e) {
      err = e;
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(err),
        level: 'error',
      });
    }
    retval = { ...retval, ...{ err } };

    return retval;
  }

  selectFilterHandle = async (val, type) => {
    this.updateCustomFilter(val, type);
  }

  updateCustomFilter = (val, type, callback = () => {}) => {
    const { filterTable } = this.state;
    const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);
    this.setState({ filterTable: updatedFilterValue }, () => callback);
  }

  changeCustomStatusButton = async (rows) => {
    const { notificationSystem } = this.props;
    const { statusSelect } = this.state;
    let status = '';
    if (statusSelect === statusSelectEnum.DRAFT) {
      status = STATUS_POST_ENUM.DRAFT;
    } else if (statusSelect === statusSelectEnum.PUBLISHED) {
      status = STATUS_POST_ENUM.PUBLISH;
    } else if (statusSelect === statusSelectEnum.TRASH) {
      status = STATUS_POST_ENUM.TRASH;
    }

    const param = {
      id_post: JSON.stringify(rows),
      status,
    };

    if (status !== '' && rows.length !== 0) {
      const res = await this.fetcher(updatePostBulk, param);
      if (res !== null) {
        notificationSystem.addNotification({
          title: 'Berhasil',
          message: 'Berhasil update status post',
          level: 'success',
        });
        this.table.resetCheckbox();
        this.table.forceRefetch();
        this.setState({ statusSelect: '' });
      }
    }
  }

  resetDateHandler() {
    this.selectFilterHandle({ start: '', end: '' }, FILTER_TYPE.DATE_RANGE);
  }

  callManageHandler() {
    const { router } = this.props;
    const { type } = this.state;
    router.push({
      pathname: `/web-portal/${type}/create`,
      state: { type },
    });
  }

  callEditDetailHandler(val) {
    const { router } = this.props;
    const { type } = this.state;
    router.push({
      pathname: `/web-portal/${type}/edit/${val.id}`,
      state: { type },
    });
  }

  changeCustomStatus(val) {
    this.setState({
      statusSelect: val,
    });
  }

  render() {
    const {
      category, statusSelect, filterTable,
    } = this.state;


    const { start, end } = getFilterValue(filterTable, FILTER_TYPE.DATE_RANGE);
    const statusFilter = getFilterValue(filterTable, FILTER_TYPE.STATUS);
    const categoryFilter = getFilterValue(filterTable, FILTER_TYPE.CATEGORY);

    return (
      <div>
        <TableWithCheckbox
          ref={(c) => {
            this.table = c;
          }}
          columns={TABLE_META}
          filters={filterTable}
          rowEvent={val => this.callEditDetailHandler(val)}
          withWrapperRender={({
            makeTable, InputSearch, PageSize, rowsSelected,
          }) => (
            <section className="panel">
              <div className="panel-heading table-header">
                <div className="row">
                  <div className="col-md-6 col-lg-7">
                    <h4 style={{ margin: '7px' }} className="panel-title">Solusi</h4>
                  </div>
                  <div className="col-md-6 col-lg-5" style={{ paddingRight: 0 }}>
                    <div style={{ float: 'left', width: 'calc(100% - 75px)' }}>
                      <CalendarPick
                        type="range"
                        startDate={start}
                        endDate={end}
                        changeEvent={(startDate, endDate) => this.selectFilterHandle({ start: startDate, end: endDate }, FILTER_TYPE.DATE_RANGE)}
                      />
                    </div>
                    <div style={{ float: 'right', padding: '2px 5px' }}>
                      <Button
                        label="Reset"
                        clickEvent={() => this.resetDateHandler()}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="panel-heading table-header">
                <div className="row">
                  <div className="col-md-3" style={{ paddingRight: 0 }}>
                    <div style={{ float: 'left', width: 'calc(100% - 100px)' }}>
                      <Select
                        data={STATUS_LIST}
                        value={statusSelect}
                        placeholder="Bulk Action"
                        changeEvent={val => this.changeCustomStatus(val)}
                        disabled={!rowsSelected.length}
                      />
                    </div>
                    <div style={{ float: 'left', padding: '2px 5px' }}>
                      <Button
                        label={`Apply ${rowsSelected.length > 0 ? `(${rowsSelected.length})` : ''}`}
                        clickEvent={() => this.changeCustomStatusButton(rowsSelected)}
                        disabled={!rowsSelected.length}
                      />
                    </div>
                  </div>
                  <div className="col-md-4">
                    <SwitchBox
                      dataset={SWITCH_LIST}
                      value={statusFilter}
                      changeEvent={value => this.selectFilterHandle(value, FILTER_TYPE.STATUS)}
                    />
                  </div>
                  <div className="col-md-2">
                    <Select
                      data={category}
                      value={categoryFilter}
                      changeEvent={value => this.selectFilterHandle(value, FILTER_TYPE.CATEGORY)}
                      classes="mb-reset"
                    />
                  </div>
                  <div className="col-md-2">
                    <InputSearch />
                  </div>
                  <div className="col-md-1">
                    <PageSize />
                  </div>
                </div>
              </div>
              <div className="panel-body">
                {makeTable()}
              </div>
            </section>
          )}
          // server-side
          onFetch={this._onFetch}
        />
      </div>
    );
  }
}

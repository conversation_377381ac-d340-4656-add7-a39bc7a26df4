import React, { useEffect, useState } from "react";
import { getFilterValue, getUpdatedFilterValue } from "../../../../utils/table.util";
import * as moment from "moment";
import * as apiSupplies from "../../../../data/setting/suppliesFormPortal";
import { catchError } from "../../../../utils/helper";
import { printExcel } from "../../../../data";

export const useSuppliesFormPortal = (props) => {
    const {
        calendar,
        assignButtons,
        assignCalendar,
        showProgress,
        hideProgress,
        notificationSystem,
        refDetail,
    } = props;
    const [filterTable, setFilterTable] = useState([
        { id: 'date', value: calendar },
    ]);
    const [dataSource, setDataSource] = useState({
        data: [],
        metadata: {
            page: 1,
            row_per_page: 10,
            total_rows: 0,
            total_page: 0,
        }
    })
    const [dataDetail, setDataDetail] = useState(null);

    const updateCustomFilter = (val, type) => {
        const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);

        setFilterTable(updatedFilterValue);
    };

    const changeDateHandle = async (start, end) => {
        assignCalendar(start, end);

        const calendar = { start, end };
        updateCustomFilter(calendar, 'date');
    };

    const handleFetch = async (state) => {
        const { page, filtered, pageSize: limit } = state;

        const filterDate = getFilterValue(filtered, 'date');

        const payload = {
            page: page + 1,
            limit,
            start: moment(filterDate.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            end: moment(filterDate.end, 'DD-MM-YYYY').format('YYYY-MM-DD')
        };

        showProgress();

        let retval = { data: [], pageCount: 0, err: null };
        try {
            const { data, metadata } = await apiSupplies.getListSupplies(payload);
            const { total_page: pageCount } = metadata;

            retval = { data, pageCount };
            setDataSource({ data, metadata });
        } catch (e) {
            retval = { ...retval, err: e };

            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }

        return retval;
    };

    const handleView = async (values) => {
        showProgress();

        try {
            const { data } = await apiSupplies.getDetailSupplies(values._original.id);
            setDataDetail(data);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const handleExport = async () => {
        showProgress();

        try {
            const [filterDate] = filterTable;

            const payloadList = {
                page: 1,
                limit: dataSource.metadata.total_rows,
                start: moment(filterDate.value.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
                end: moment(filterDate.value.end, 'DD-MM-YYYY').format('YYYY-MM-DD')
            };

            const { data: dataList } = await apiSupplies.getListSupplies(payloadList);

            const template = 'laporan_supplies_form_portal.xlsx';
            const outputName = 'laporan_supplies_form_portal';
            const alias = 'x';

            const variable = {
                mulai: moment(filterDate.value.start, 'DD-MM-YYYY').format('DD/MM/YYYY'),
                akir: moment(filterDate.value.end, 'DD-MM-YYYY').format('DD/MM/YYYY'),
                dateNow: moment().format('DD MMM YYYY'),
                list: dataList,
            };

            const payload = {
                param: [{
                    variable,
                    template,
                    output_name: outputName,
                    data: dataList,
                    alias,
                }],
            };

            const res = await printExcel(payload);
            const { data } = res;
            window.location = data;
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    const handleResetDetail = () => setDataDetail(null);

    useEffect(() => {
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span style={{ display: 'flex', alignItems: 'center' }}>
                        <i className="fa fa-file-download-white" />
                        {' '}
                        Download Data
                    </span>
                ),
                action: () => handleExport(),
            },
        ]);
        assignCalendar(null, null, (startDate, endDate) => {
            changeDateHandle(startDate, endDate);
        });
    }, []);

    useEffect(() => {
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span style={{ display: 'flex', alignItems: 'center' }}>
                        <i className="fa fa-file-download-white" />
                        {' '}
                        Download Data
                    </span>
                ),
                action: () => handleExport(),
            },
        ]);
    }, [dataSource.metadata.total_rows]);

    useEffect(() => {
        if (dataDetail) refDetail.current.showPopup();
    }, [dataDetail]);

    return {
        filterTable,
        handleFetch,
        handleView,
        dataDetail,
        handleResetDetail,
    }
}
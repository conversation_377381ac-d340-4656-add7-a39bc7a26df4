import React, { Fragment, useRef } from 'react';

import CoreHOC from '../../../../core/CoreHOC';
import SidePopup from '../../../../components/sidepopup/ContainerV2';
import Table from '../../../../components/table/v.2/Table';

import { useSuppliesFormPortal } from './useSuppliesFormPortal';
import { columns } from './config/table';
import Detail from './Detail';

const SuppliesFormPortal = (props) => {
    const refDetail = useRef();
    const {
        filterTable,
        handleFetch,
        handleView,
        dataDetail,
        handleResetDetail,
    } = useSuppliesFormPortal({ ...props, refDetail });

    return (
        <Fragment>
            <Table
                columns={columns}
                rowEvent={handleView}
                filters={filterTable}
                withWrapperRender={({ makeTable, PageSize }) => (
                    <section className="panel">
                        <div className="panel-heading table-header">
                            <div className="row">
                                <div className="col-md-6">
                                    <h4 className="panel-title" style={{ paddingTop: '8px' }}>Supplies Form Portal</h4>
                                </div>
                                <div className="col-md-6">
                                    <PageSize />
                                </div>
                            </div>
                        </div>
                        <div className="panel-body">
                            {makeTable()}
                        </div>
                    </section>
                )}

                onFetch={handleFetch}
            />

            <SidePopup
                ref={refDetail}
                width={600}
                onHide={handleResetDetail}
                render={({ show }) => {
                    if (show) return <Detail data={dataDetail} />

                    return (null);
                }}
            />
        </Fragment>
    )
}

export default CoreHOC(SuppliesFormPortal);

import React, { Fragment, useRef } from 'react';
import { get } from 'lodash';

import CoreHOC from '../../../../core/CoreHOC';
import Table from '../../../../components/table/v.2/Table';
import Select from '../../../../components/form/Select';
import DeleteConfirm from '../../../../components/modalpopup/DeleteConfirm';
import { usePromo } from './usePromo';
import { columns } from './config/table';
import SidePopup from '../../../../components/sidepopup/ContainerV2';
import SidePopupForm from './components/SidePopupForm';

const Promo = (props) => {
    const sidePopupRef = useRef();
    const deleteConfirmRef = useRef();
    const tableRef = useRef();
    const {
        filterTable,
        handleFetch,
        categories,
        saveHandler,
        dataDetail,
        changeForm,
        changeFilter,
        statusOptions,
        onRowAction,
        deleteUnusedFile,
        handleDeletePromo,
    } = usePromo({ ...props, sidePopupRef, tableRef, deleteConfirmRef });

    return (
        <Fragment>
            <Table
                ref={tableRef}
                columns={columns(onRowAction)}
                withWrapperRender={({ makeTable, PageSize, InputSearch }) => (
                    <section className="panel">
                        <div className="panel-heading table-header">
                            <div className="row">
                                <div className="col-md-6">
                                    <h4 className="panel-title" style={{ paddingTop: '8px' }}>Promo</h4>
                                </div>
                                <div className="col-md-6">
                                    <div className="d-flex" style={{ gap: 8 }}>
                                        <InputSearch />
                                        <div style={{ width: 200 }}>
                                            <Select
                                                data={categories}
                                                value={filterTable.categoryId}
                                                changeEvent={value => changeFilter('categoryId', value)}
                                            />
                                        </div>
                                        <div style={{ width: 200 }}>
                                            <Select
                                                data={statusOptions}
                                                value={filterTable.status}
                                                changeEvent={value => changeFilter('status', value)}
                                            />
                                        </div>
                                        <div style={{ marginLeft: '-15px' }}>
                                            <PageSize />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="panel-body">
                            {makeTable()}
                        </div>
                    </section>
                )}

                onFetch={handleFetch}
            />
            <DeleteConfirm
                title="Confirmation"
                confirmText="Yes, Delete Promo"
                cancelText="Cancel"
                confirmHandle={() => handleDeletePromo(dataDetail)}
                ref={deleteConfirmRef}
            >
                Are you sure to delete promo
                {' '}
                <b>{get(dataDetail, 'title', '')}</b> ?
            </DeleteConfirm>

            <SidePopup
                ref={sidePopupRef}
                width={700}
                saveHandle={saveHandler}
                onHide={() => deleteUnusedFile()}
                render={({ show, resetValidation, validateForm }) => {
                    if (show) {
                        return (
                            <SidePopupForm
                                data={dataDetail}
                                categories={categories.filter(x => x.id)}
                                validateInput={resetValidation}
                                validateForm={validateForm}
                                changeEvent={changeForm}
                            />
                        );
                    }
                    return null;
                }}
            />
        </Fragment>
    );
};

export default CoreHOC(Promo);

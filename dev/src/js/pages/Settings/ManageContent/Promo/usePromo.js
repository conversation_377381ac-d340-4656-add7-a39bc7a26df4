import React, { useCallback, useEffect, useState } from 'react';
import * as moment from 'moment';
import { getFilterValue } from '../../../../utils/table.util';
import { catchError } from '../../../../utils/helper';
import * as PromoAPI from '../../../../data/setting/promo';
import { ACTION } from './config/table';
import { getNameFile, setFileUpload } from './utils';
import { deleteFileUpload } from '../../../../data/portal';

const defaultFormData = {
    slug: '',
    category_id: '',
    thumbnail: setFileUpload(),
    title: '',
    desc: '',
    meta_desc: '',
    content: '',
    snk: '',
    start_date: moment().format('DD/MM/YYYY'),
    end_date: moment().format('DD/MM/YYYY'),
    sequence: undefined,
    is_active: 1,
};

export const usePromo = (props) => {
    const {
        assignButtons,
        assignCalendar,
        showProgress,
        hideProgress,
        notificationSystem,
        sidePopupRef,
        tableRef,
        deleteConfirmRef,
    } = props;

    const [filterTable, setFilterTable] = useState({
        status: '',
        categoryId: '',
        search: '',
    });
    const [dataSource, setDataSource] = useState({
        data: [],
        metadata: {
            page: 1,
            row_per_page: 10,
            total_rows: 0,
            total_page: 0,
        },
    });
    const [categories, setCategories] = useState([]);
    const [dataDetail, setDataDetail] = useState(defaultFormData);
    const statusOptions = [
        { id: '', name: 'All Status' },
        { id: 0, name: 'Inactive' },
        { id: 1, name: 'Active' },
        { id: 2, name: 'Expired' },
    ];

    const refetch = useCallback((isResetPage = false) => {
        setTimeout(() => tableRef.current.forceRefetch(isResetPage), 500);
    }, [tableRef]);

    const changeFilter = (key, value) => setFilterTable(prev => ({ ...prev, [key]: value }));

    const fetchCategories = async () => {
        try {
            const { data } = await PromoAPI.getListCategoriesPromo();
            const mapData = [{ id: '', name: 'All Category' }];
            (data || []).map((x) => {
                mapData.push({ id: x.id, name: x.title });
                return x;
            });
            setCategories(mapData);
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(e),
                level: 'error',
            });
        }
    };

    const handleFetch = async (state) => {
        const {
            page, filtered, pageSize: limit, sorted,
        } = state;
        const search = getFilterValue(filtered, 'all');
        const payload = {
            page: page + 1,
            limit,
            ...(filterTable.categoryId && { category_id: filterTable.categoryId }),
            ...(filterTable.status !== '' && { status: filterTable.status }),
            ...(search && { search }),
            ...(sorted.length > 0 && { sort: `${sorted[0].desc ? '-' : ''}${sorted[0].id}` }),
        };

        showProgress();

        let retval = { data: [], pageCount: 0, err: null };
        try {
            const { data, metadata } = await PromoAPI.getListPromo(payload);
            const { total_page: pageCount } = metadata;

            retval = { data, pageCount };
            setDataSource({ data, metadata });
        } catch (e) {
            retval = { ...retval, err: e };
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(e),
                level: 'error',
            });
        } finally {
            hideProgress();
        }

        return retval;
    };

    const openDetailPromo = async (data) => {
        try {
            showProgress();
            const { data: resData } = await PromoAPI.getDetailPromo(data.id);
            setDataDetail({
                    ...resData,
                    thumbnailOrigin: resData.thumbnail,
                    thumbnail: setFileUpload(resData.thumbnail),
                    start_date: moment(resData.start_date).format('DD/MM/YYYY'),
                    end_date: moment(resData.end_date).format('DD/MM/YYYY'),
            }, sidePopupRef.current.showPopup());
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    const handleDeletePromo = async (data) => {
        try {
            showProgress();
            const { message } = await PromoAPI.deletePromo(data.id);
            notificationSystem.addNotification({
                title: 'Success',
                message,
                level: 'success',
            });
            setDataDetail(defaultFormData);
            refetch();
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            deleteConfirmRef.current.hidePopup();
            hideProgress();
        }
    };

    const onRowAction = (act, data) => {
        switch (act) {
            case ACTION.EDIT:
                openDetailPromo(data);
                break;
            case ACTION.DELETE:
                deleteConfirmRef.current.showPopup();
                setDataDetail(data);
                break;
            default:
                break;
        }
    };

    const saveHandler = async () => {
        try {
            const savePromo = dataDetail.id ? PromoAPI.updatePromo : PromoAPI.createPromo;
            showProgress();
            const payload = {
                ...dataDetail,
                start_date: moment(dataDetail.start_date, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                end_date: moment(dataDetail.end_date, 'DD/MM/YYYY').format('YYYY-MM-DD'),
                thumbnail: getNameFile(dataDetail.thumbnail.fullpath),
                ...(dataDetail.content && { content: btoa(dataDetail.content) }),
                ...(dataDetail.snk && { snk: btoa(dataDetail.snk) }),
                ...(dataDetail.meta_desc && { meta_desc: btoa(dataDetail.meta_desc) }),
            };
            const { message } = await savePromo(payload);
            setDataDetail(prev => ({ ...prev, isSaved: true }));
            notificationSystem.addNotification({
                title: 'Success',
                message,
                level: 'success',
            });
            sidePopupRef.current.hidePopup();
            refetch();
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal',
                message: catchError(err),
                level: 'error',
            });
            sidePopupRef.current.failedCallback();
        } finally {
            hideProgress();
        }
    };

    const openAddPromo = () => setDataDetail(defaultFormData, sidePopupRef.current.showPopup());

    const deleteUnusedFile = () => new Promise((resolve) => {
        const {
            thumbnailOrigin, id, thumbnail: { fullpath }, isSaved,
        } = dataDetail;
        if (!isSaved && fullpath && (!id || (thumbnailOrigin && thumbnailOrigin !== fullpath))) {
            const url = new URL(fullpath);
            deleteFileUpload({ path: url.pathname }).finally(resolve());
        }
        resolve();
    });

    const changeForm = async (key, val) => {
        if (key === 'thumbnail') {
            await deleteUnusedFile();
        }
        setDataDetail(prev => ({ ...prev, [key]: val }));
    };

    useEffect(() => {
        assignButtons([
            {
                type: 'primary',
                content: (
                    <div className="d-flex align-items-center">
                        Add Promo
                    </div>
                ),
                action: openAddPromo,
            },
        ]);
        assignCalendar(null, null, null);
        fetchCategories();
    }, []);

    useEffect(() => refetch(true), [filterTable]);

    return {
        filterTable,
        handleFetch,
        dataDetail,
        setDataDetail,
        categories,
        saveHandler,
        changeFilter,
        statusOptions,
        onRowAction,
        dataSource,
        deleteUnusedFile,
        changeForm,
        handleDeletePromo,
    };
};

import React from 'react';
import DateColumn from '../../../../../components/table/components/DateColumn';
import { styled } from '../../../../../stitches.config';
import RowMenuAction from '../../../../Sales/Supplies/SuppliesProduct/components/RowMenuAction';

const TextEllipsis = styled('div', {
    '-webkit-line-clamp': 2,
    display: '-webkit-box',
    '-webkit-box-orient': 'vertical',
    overflow: 'hidden',
    maxWidth: 300,
    wordBreak: 'break-all',
});

const STATUS_ENUM = {
    INACTIVE: 0,
    ACTIVE: 1,
    EXPIRED: 2,
};

export const ACTION = {
    EDIT: 'edit',
    DELETE: 'delete',
};

const STATUS_LABEL = {
    [STATUS_ENUM.INACTIVE]: 'Inactive',
    [STATUS_ENUM.ACTIVE]: 'Active',
    [STATUS_ENUM.EXPIRED]: 'Expired',
};

export const columns = onRowAction => ([
    {
        Header: 'Title',
        accessor: 'title',
    },
    {
        Header: 'Description',
        accessor: 'desc',
        Cell: ({ value }) => <TextEllipsis dangerouslySetInnerHTML={{ __html: value }} />,
    },
    {
        Header: 'start date',
        accessor: 'start_date',
        Cell: DateColumn,
    },
    {
        Header: 'end date',
        accessor: 'end_date',
        Cell: DateColumn,
    },
    {
        Header: 'Status',
        accessor: 'is_active',
        sortable: false,
        Cell: ({ value }) => STATUS_LABEL[value],
    },
    {
        Header: 'Action',
        accessor: 'id',
        width: 100,
        sortable: false,
        Cell: (props) => {
            const { row: { _original } } = props;
            return (
                <RowMenuAction
                    options={[
                        {
                            title: 'Edit',
                            onClick: () => onRowAction(ACTION.EDIT, _original),
                        },
                        {
                            title: 'Delete',
                            onClick: () => onRowAction(ACTION.DELETE, _original),
                        },
                    ]}
                />
            );
        },
    },
]);

import DateTimeColumn from '../../../../components/table/components/DateTimeColumn';

const TABLE_META = [
    {
        Header: 'Title',
        accessor: 'title',
    },
    {
        Header: 'Author',
        accessor: 'author_name',
    },
    {
        Header: 'Category',
        accessor: 'category_name',
    },
    {
        Header: 'Highlight',
        accessor: 'is_highlight',
    },
    {
        Header: 'SEQ',
        accessor: 'news_post_seq',
    },
    {
        Header: 'Date',
        accessor: 'createdate',
        cell: DateTimeColumn,
    },
    {
        Header: 'Id',
        accessor: 'id',
    },
];

const STATUS_ENUM = {
    DRAFT: 'Draft',
    PUBLISHED: 'Published',
    TRASH: 'Trash',
    BULK: 'Bulk Action',
};

const STATUS_LIST = [
    { label: STATUS_ENUM.PUBLISHED, value: STATUS_ENUM.PUBLISHED },
    { label: STATUS_ENUM.DRAFT, value: STATUS_ENUM.DRAFT },
    { label: STATUS_ENUM.TRASH, value: STATUS_ENUM.TRASH },
];

const SWITCH_LIST = [
    { text: 'All', value: 'all' },
    { text: STATUS_ENUM.PUBLISHED, value: 'published' },
    { text: STATUS_ENUM.DRAFT, value: 'draft' },
    { text: STATUS_ENUM.TRASH, value: 'trash' },
];

const FILTER_TYPE = {
    DATE_RANGE: 'dateRange',
    STATUS: 'filterStatus',
    CATEGORY: 'filterCategory',
};

const STATUS_POST_ENUM = {
    DRAFT: 'draft',
    PUBLISH: 'publish',
    TRASH: 'trash',
    CANCEL: 'cancel',
};

export {
    TABLE_META, STATUS_LIST, SWITCH_LIST, FILTER_TYPE, STATUS_ENUM, STATUS_POST_ENUM,
};

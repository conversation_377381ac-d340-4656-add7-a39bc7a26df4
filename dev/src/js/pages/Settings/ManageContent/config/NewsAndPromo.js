import DateTimeColumn from '../../../../components/table/components/DateTimeColumn';

const TABLE_META = [
    {
        Header: 'Title',
        accessor: 'title',
    },
    {
        Header: 'Author',
        accessor: 'author_name',
    },
    {
        Header: 'Type',
        accessor: 'type',
    },
    {
        Header: 'Highlight',
        accessor: 'is_highlight',
    },
    {
        Header: 'SEQ',
        accessor: 'news_post_seq',
    },
    {
        Header: 'Date',
        accessor: 'createdate',
        cell: DateTimeColumn,
    },
    {
        Header: 'Id',
        accessor: 'id',
    },
];

const STATUS_LIST = [
    { label: 'Published', value: 'Published' },
    { label: 'Draft', value: 'Draft' },
    { label: 'Trash', value: 'Trash' },
];

const SWITCH_LIST = [
    { text: 'All', value: 'all' },
    { text: 'Published', value: 'published' },
    { text: 'Draft', value: 'draft' },
    { text: 'Trash', value: 'trash' },
];

const FILTER_TYPE = {
    DATE_RANGE: 'dateRange',
    STATUS: 'filterStatus',
    TYPE: 'filterType',
};

const FILTER_TYPE_LIST = [
    { id: 'news,promo', value: 'News & Promo' },
    { id: 'news', value: 'News' },
    { id: 'promo', value: 'Promo' },
];

const POST_TYPE = [
    { id: 'news', value: 'News' },
    { id: 'promo', value: 'Promo' },
];

const SAVE_TYPE = {
    TRASH: 'trash',
    PUBLISH: 'publish',
    PREVIEW: 'preview',
    CANCEL: 'cancel',
    DRAFT: 'draft',
};


export {
    TABLE_META, STATUS_LIST, SWITCH_LIST, FILTER_TYPE_LIST, FILTER_TYPE, POST_TYPE, SAVE_TYPE,
};

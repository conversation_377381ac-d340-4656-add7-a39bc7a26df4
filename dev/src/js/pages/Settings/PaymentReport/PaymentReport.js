import React, { useRef, useState } from 'react'
import Table from '../../../components/table/v.2/Table';
import InputSelect from '../../../components/form/Select';
import { StatusFilter, TableColumn } from './utils';
import usePaymentList from './hooks/usePaymentList';
import InputNumber from '../../../components/form/InputNumber';
import InputText from '../../../components/form/InputText';
import useCheckStatus from './hooks/useCheckStatus';
import ModalPopup from '../../../components/modalpopup/v.2/Container';
import SidePopup from '../../../components/sidepopup/ContainerV2';
import DetailPopup from './DetailPopup';

const PaymentReport = (props) => {
    const popupRef = useRef()
    const [detail, setDetail] = useState({})
    const {
        onFetch,
        updateFilterTable,
        filterTable,
        debounceFilter,
        handleUpdateDebounceFilter
    } = usePaymentList(props);
    const { onCheck, modal, setModal } = useCheckStatus(props);
    const [va, amount] = debounceFilter;

    return (
        <div>
            <Table
                filters={filterTable}
                columns={TableColumn(({ data }) => { setDetail(data); popupRef.current.showPopup(); })}
                onFetch={onFetch}
                withWrapperRender={({
                    makeTable, InputSearch, PageSize,
                }) => (
                    <section className="panel">
                        <div className="panel-heading table-header">
                            <h4 className="panel-title" style={{ paddingTop: '10px' }}>Laporan Pembayaran</h4>
                            <div className="panel-heading" style={{ marginBottom: '25px', marginTop: '10px' }}></div>
                            <div className="d-flex" style={{ justifyContent: "space-between" }}>
                                <div className="d-flex" style={{ gap: '10px' }}>
                                    <InputNumber
                                        placeholder="Amount"
                                        changeEvent={(val, e) => {
                                            if (!e) return;
                                            handleUpdateDebounceFilter('amount', val)
                                        }}
                                        value={amount.value}
                                    />
                                    <InputText
                                        placeholder="VA/Link Payment"
                                        changeEvent={e => handleUpdateDebounceFilter('va_or_link', e)}
                                        defaultValue=''
                                    />
                                    <div style={{ width: '200px' }}>
                                        <InputSelect
                                            changeEvent={e => updateFilterTable('status', e)}
                                            style={{ width: '200px' }}
                                            data={StatusFilter}
                                        />
                                    </div>
                                </div>
                                <div className="d-flex">
                                    <InputSearch />
                                    <PageSize />
                                </div>
                            </div>
                        </div>
                        <div className="panel-body">
                            {makeTable()}
                        </div>
                    </section>
                )}
            />
            <ModalPopup
                headerTitle="Information"
                confirmText="Oke"
                show={modal.show}
                hideBack
                onHide={() => setModal({ wording: '', show: false })}
                confirmHandle={(cb) => { setModal({ wording: '', show: false }); cb(); }}
            >
                {modal.wording}
            </ModalPopup>
            <SidePopup
                ref={(c) => { popupRef.current = c; }}
                width={560}
                type="edit"
                btnSaveText="Cek Status"
                saveHandle={() => onCheck(detail.transaction_no).finally(() => { popupRef.current.hidePopup(); })}
                render={() => (
                    <DetailPopup data={detail} />
                )}
            />
        </div>
    )
}

export default PaymentReport
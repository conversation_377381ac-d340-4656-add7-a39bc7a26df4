import React from 'react'
import { DetailColumn, StatusColumn } from "./components/Columns"
import { numSeparator } from '../../../utils/helper';

export const TableColumn = onClick => [
    {
        Header: 'Nomor Transaksi',
        accessor: 'transaction_no',
        minWidth: 120
    },
    {
        Header: 'Referensi Transaksi',
        accessor: 'transaction_no_order',
        minWidth: 120
    },
    {
        Header: 'Nama Customer',
        accessor: 'customer_name',
    },
    {
        Header: 'Total Amount',
        accessor: 'transaction_total',
        Cell: ({ value }) => `Rp ${numSeparator(value)}`
    },
    {
        Header: 'Channel',
        accessor: 'channel',
    },
    {
        Header: 'Status',
        accessor: 'status',
        Cell: StatusColumn,
    },
    {
        Header: 'Action',
        accessor: 'action',
        Cell: ({ original }) => <DetailColumn onClick={() => onClick({ data: original })} />,
        minWidth: 120
    },
];

export const StatusFilter = [
    {
        id: '',
        name: 'Semua Status',
    },
    {
        id: '0',
        name: 'Open',
    },
    {
        id: '1',
        name: '<PERSON><PERSON>',
    },
    {
        id: '2',
        name: 'Failed',
    },
    {
        id: '3',
        name: 'Success',
    },
    {
        id: '5',
        name: 'Expired',
    },
];

export const OrderResponseCode = {
    '52': value => `Transaksi ${value} berhasil`,
    '53': value => `Transaksi ${value} dibatalkan`,
    '54': value => `Transaksi ${value} sudah melewati batas pembayaran`,
    '31': value => `Transaksi ${value} belum dibayarkan oleh User`,
    '55': value => `Transaksi ${value} gagal`,
}
import { useState, useEffect } from 'react';
import { getList } from '../../../../data/payment_report';
import { catchError } from '../../../../utils/helper';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';
import * as moment from 'moment';

const usePaymentList = (props) => {
    const { calendar: { start, end }, assignCalendar, assignButtons } = props;
    const [filterTable, setFilterTable] = useState([
        { id: 'date', value: { start, end } },
        { id: 'all', value: '' },
        { id: 'va_or_link', value: '' },
        { id: 'amount', value: '' },
        { id: 'status', value: '' }
    ]);
    const [debounceFilter, setDebounceFilter] = useState([
        { id: 'va_or_link', value: '' },
        { id: 'amount', value: '' },
    ]);
    const [va, amount] = debounceFilter;

    const onFetch = async (tableQuery) => {
        const { filtered, pageSize, page } = tableQuery;

        const filterDate = getFilterValue(filtered, 'date');
        const filterVA = getFilterValue(filtered, 'va_or_link');
        const filterAmount = getFilterValue(filtered, 'amount');
        const filterStatus = getFilterValue(filtered, 'status');
        const filterKeyword = getFilterValue(filtered, 'all');

        let payload = {
            limit: pageSize,
            page: parseInt(page, 10) + 1,
            start_date: moment(filterDate.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            end_date: moment(filterDate.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            ...filterVA && { va_or_link: filterVA },
            ...filterAmount && { amount: filterAmount },
            ...filterStatus && { status: filterStatus },
            ...filterKeyword && { search: filterKeyword },
        };

        try {
            const response = await getList(payload);

            return { data: response.data, pageCount: response.meta.last_page }
        } catch (err) {
            props.notificationSystem.addNotification({
                title: 'Failed to check status transaction',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    const updateFilterTable = (type, value) => {
        const newFilterTable = getUpdatedFilterValue(filterTable, type, value);

        setFilterTable(newFilterTable);
    }

    const changeDateHandler = (start, end) => {
        assignCalendar(start, end);

        updateFilterTable('date', { start, end });
    }

    const handleUpdateDebounceFilter = (type, value) => {
        const tempFilter = [...debounceFilter];

        const selectedFilter = tempFilter.find(x => x.id === type);
        selectedFilter.value = value;

        setDebounceFilter(tempFilter);
    }

    useEffect(() => {
        const timeout = setTimeout(() => {
            updateFilterTable('va_or_link', va.value);
        }, 500)

        return () => {
            clearTimeout(timeout)
        }
    }, [va.value, 500])

    useEffect(() => {
        const timeout = setTimeout(() => {
            updateFilterTable('amount', amount.value);
        }, 500)

        return () => {
            clearTimeout(timeout)
        }
    }, [amount.value, 500]);

    useEffect(() => {
        assignCalendar(null, null, (startDate, endDate) => {
            changeDateHandler(startDate, endDate);
        });
        assignButtons([]);
    }, [])

    return {
        onFetch,
        updateFilterTable,
        filterTable,
        setDebounceFilter,
        debounceFilter,
        handleUpdateDebounceFilter,
    };
}

export default usePaymentList;

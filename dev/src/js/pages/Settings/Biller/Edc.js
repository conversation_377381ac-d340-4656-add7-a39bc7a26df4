import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import update from 'immutability-helper';

import CoreHOC from '../../../core/CoreHOC';

import Table from '../../../components/table/v.2/Table';
import * as edcApi from '../../../data/edc';

import { TABLE_META } from './config/edc';

import { catchError } from '../../../utils/helper';

class Edc extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    hideProgress: PropTypes.func.isRequired,
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
  }

  static defaultProps = {
    assignCalendar: null,
    assignButtons: null,
    router: {
      push: null,
    },
    notificationSystem: ({
      addNotification: () => {
        // do nothing
      },
    }),
  }

  constructor(props) {
    super(props);

    this.state = {
      list: [],
    };
  }

  componentWillMount() {
    const {
      assignCalendar, assignButtons,
    } = this.props;
    assignCalendar(null, null, null);
    assignButtons([
      {
        type: 'primary',
        content: (
          <span>
            <i className="fa fa-plus" />
            {' '}
            Tambah Pengajuan EDC
          </span>
        ),
        action: () => {
          this.callCreateHandler();
        },
      },
    ]);
    this.fetchData();
  }

  fetchData = async () => {
    const { notificationSystem, hideProgress } = this.props;

    try {
      const res = await edcApi.getEdcList();
      if (!res.status) throw new Error(res.msg);
      const { data } = res;

      const newList = data.details.map(x => (
        update(x, {
          createdate: { $set: x.createdate || '-' },
          updatedate: { $set: x.updatedate || '-' },
          notes: { $set: x.notes || '-' },
        })
      ));

      this.setState({
        list: newList,
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(err),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  callCreateHandler = () => {
    const { router } = this.props;
    router.push('/non-cash-setting/wallet-edc/create');
  }

  callEditDetailHandler = ({ _original: data }) => {
    const { router } = this.props;
    router.push(`/non-cash-setting/wallet-edc/detail/${data.merchant_edc_no}`);
  }

  render() {
    const { list } = this.state;

    return (
      <div>
        <Table
          ref={(c) => { this.table = c; }}
          columns={TABLE_META}
          content={list}
          rowEvent={this.callEditDetailHandler}
          withWrapperRender={({
            makeTable, InputSearch, PageSize,
          }) => (
            <section className="panel">
              <div className="panel-heading table-header">
                <div className="row">
                  <div className="col-md-9">
                    <h4 className="title">Pengajuan EDC</h4>
                  </div>
                  <div className="col-md-2">
                    <InputSearch />
                  </div>
                  <div className="col-md-1">
                    <PageSize />
                  </div>
                </div>
              </div>
              <div className="panel-body">
                {makeTable()}
              </div>
            </section>
          )}
        />
      </div>
    );
  }
}

export default CoreHOC(Edc);

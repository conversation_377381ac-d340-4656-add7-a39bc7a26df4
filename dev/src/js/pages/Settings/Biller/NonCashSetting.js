import React, { Component } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';

import CoreHOC from '../../../core/CoreHOC';
import Table, { getFilterValue } from '../../../components/table/v.2/Table';
import SidePopup from '../../../components/sidepopup/Container';
import InputText from '../../../components/form/InputText';
import InputPercent from '../../../components/form/InputPercent';
import Switch from '../../../components/form/Switch';
import SwitchBox from '../../../components/form/SwitchBox';
import ModalPopup from '../../../components/modalpopup/Container';
import SelectMultiple from '../../../components/form/SelectMultiple';
import SwitchBoxMultiple from '../../../components/form/SwitchBoxMultiple';

import SelectStatusNonCash from './components/SelectStatusNonCash';

import * as walletApi from '../../../data/wallet';

import {
    tableMetaWithParam,
    SwitchData,
    dataStatusWallet
} from './config/nonCashSetting';

class NoCashSetting extends Component {
    static propTypes = {
        router: PropTypes.shape({
            push: PropTypes.func,
        }).isRequired,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        hideProgress: PropTypes.func.isRequired,
    };

    static defaultProps = {
        notificationSystem: {
            addNotification: () => {},
        },
    };

    constructor(props) {
        super(props);

        this.state = {
            checkAll: false,
            totalCheck: 0,
            content: [],
            idDetail: '',
            namaUsaha: '',
            outlet: '',
            status: false,
            cashNonTunai: '',
            switchFilter: '',
            bank: '',
            merchant: '',
            bukuKas: '',
            mdr: '',
            customer: '',
            buttonChecklist: false,
            allStatus: '',
            page: 1,
            confirmText: false,
            provider: [],
            filterStatus: [
                { text: 'aktif', value: true },
                { text: 'nonaktif', value: true },
            ],
            resultStatusWallet: '1',
            providerList: [],
        };

        this.checkAllHandler = this.checkAllHandler.bind(this);
        this.checkHandler = this.checkHandler.bind(this);
    }

    componentWillMount() {
        this.fetchBank();
    }

    componentDidMount = () => {
        const { assignButtons, assignFilterColoumn, assignRangeDate, assignCalendar } = this.props;
        assignCalendar(null, null, null);
        assignButtons();
        assignFilterColoumn([]);
        assignRangeDate([]);
    };

    componentDidUpdate(prevProps, prevState) {
        const { filterStatus, provider } = this.state;

        if (
            prevState.filterStatus[0].value !== filterStatus[0].value ||
            prevState.filterStatus[1].value !== filterStatus[1].value
        ) {
            this.table.forceRefetch();
        }

        if (prevState.provider.length !== provider.length) {
            clearTimeout(this.timeOut);
            this.timeOut = setTimeout(() => {
                this.table.forceRefetch();
            }, 500);
        } else {
            let providerIsDifferent = false;

            for (let i = 0; i < provider.length; i++) {
                if (prevState.provider[i] !== provider[i]) {
                    providerIsDifferent = true;
                }

                if (providerIsDifferent) break;
            }

            if (providerIsDifferent) {
                clearTimeout(this.timeOut);
                this.timeOut = setTimeout(() => {
                    this.table.forceRefetch();
                }, 500);
            }
        }
    }

    fetchDataHandler = async state => {
        const { provider, filterStatus } = this.state;

        const { showProgress, hideProgress, notificationSystem } = this.props;
        const { page, pages, filtered, pageSize, sorted } = state;

        const filterKeyword = getFilterValue(filtered, 'all');

        let payload = {
            limit: pageSize,
            page: page + 1,
            ...(filterKeyword && { search: filterKeyword }),
            provider: JSON.stringify(provider),
        };
        Object.assign(payload, { status: '' });

        const aktifValue = filterStatus[0].value;
        const inaktifValue = filterStatus[1].value;
        if (inaktifValue && !aktifValue) {
            Object.assign(payload, { status: 0 });
        } else if (aktifValue && !inaktifValue) {
            Object.assign(payload, { status: 1 });
        } else {
            delete payload.status;
        }

        // get filter params
        if (filtered && filtered.length > 0) {
            const filterAll = filtered.find(data => data.id === 'all');
            payload = { ...payload, ...{ search: filterAll.value } };
        }

        if (sorted.length > 0) {
            const { [sorted.length - 1]: { id, desc } } = sorted;
            
            Object.assign(payload, {
                column: id,
                isAsc: desc ? 'DESC' : 'ASC',
            });
        }

        const err = null;
        let retval = { data: [], pageCount: pages > -1 ? pages : -1 };

        showProgress();

        try {
            const res = await walletApi.fetchNoPaymentCash(payload);

            if (!res) throw new Error('Gagal Mendapatkan Data');
            retval = {
                data: res.data,
                pageCount: Math.ceil(res.total / pageSize),
            };
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal Mendapatkan Data',
                message: catchError(e),
                level: 'error',
            });
        }

        hideProgress();

        retval = Object.assign({}, retval, { err });

        return retval;
    };

    callActionHandler = value => {
        const { _original: formData } = value;
        const cashNonTunai = `${formData.provider_name} ${formData.tcash_qr_snap}`;
        let switchFilter = '1';
        if (formData.tcash_qr_snap === 'Dynamic') {
            switchFilter = '3';
        }

        let bukuKas = '';
        if (formData.akunting_code) {
            bukuKas = `${formData.akunting_code} | ${formData.akunting_name}`;
        }

        this.setState(
            {
                idDetail: formData.id,
                namaUsaha: formData.usaha_name || '',
                outlet: formData.outlet_name || '',
                status: formData.status === '1',
                cashNonTunai,
                switchFilter,
                bank: formData.provider_name || '',
                merchant: formData.merchant_code || '',
                bukuKas,
                mdr: formData.mdr || '',
                customer: formData.tambahan_biaya || '',
            },
            () => {
                this.sidePop.showPopup();
            }
        );
    };

    checkAllHandler = value => {
        const data = this.table.getterFakeContent();
        const newContent = [];
        let buttonChecklist = false;
        let totalCheck = 0;

        if (value) {
            data.forEach(l => {
                newContent.push(l.id);
                totalCheck += 1;
            });

            buttonChecklist = true;
        }

        this.setState({
            checkAll: value,
            content: newContent,
            resultStatusWallet: '1',
            totalCheck,
            buttonChecklist
        });
    };

    checkHandler = id => {
        const { content, totalCheck } = this.state;
        const data = this.table.getterFakeContent();
        let buttonChecklist = false;
        let newTotalCheck = 0;

        const newIdSelected = Array.from(content);

        const index = newIdSelected.indexOf(id);
        if (index === -1) {
            newIdSelected.push(id);
            newTotalCheck = totalCheck + 1;
        } else {
            newIdSelected.splice(index, 1);
            newTotalCheck = totalCheck - 1;
        }

        if (newIdSelected.length) {
            buttonChecklist = true;
        }

        this.setState({
            content: newIdSelected,
            checkAll: newIdSelected.length === data.length,
            totalCheck: newTotalCheck,
            buttonChecklist,
            resultStatusWallet: '1',
        });
    };

    showPopupChangeStatus = () => {
        const { resultStatusWallet } = this.state;
        let confirmText = false;
        if (resultStatusWallet === '1') {
            confirmText = true;
        }

        this.setState(
            {
                allStatus: resultStatusWallet,
                confirmText,
            },
            () => {
                this.actionPopup.showPopup();
            }
        );
    };

    updateStatus = () => {
        const { allStatus, content } = this.state;
        const checkListArray = [];

        content.forEach(id => {
            checkListArray.push(parseInt(id, 10));
        });

        this.updateData(checkListArray, allStatus);
        this.actionPopup.hidePopup();
    };

    saveHandler = id => {
        const { status } = this.state;
        let aktifStatus = '0';
        if (status) {
            aktifStatus = '1';
        }

        this.updateData([id], aktifStatus);
        this.sidePop.hidePopup();
    };

    updateData = (id, status) => {
        const { router, notificationSystem } = this.props;
        const param = {
            id: JSON.stringify(id),
            status,
        };

        walletApi.updateNoPaymentCash(param).then(
            () => {
                this.setState({
                    content: [],
                    totalCheck: 0,
                    buttonChecklist: false,
                    checkAll: false
                }, () => {
                    notificationSystem.addNotification({
                        title: 'Berhasil Update Data',
                        level: 'success',
                    });
                    this.table.forceRefetch();
                });
            },
            message => {
                if (!message) {
                    router.push('/auth/login');
                } else {
                    notificationSystem.addNotification({
                        title: 'Gagal mengupdate data',
                        message: '',
                        level: 'error',
                    });
                }
            }
        );
    };

    changeSelectStatusUpdate = resultStatusWallet => {
        this.setState({
            resultStatusWallet,
        });
    };

    fetchBank() {
        const { router, notificationSystem } = this.props;

        walletApi.fetchPaymentList().then(
            response => {
                const providerList = [];
                response.data.forEach(e => {
                    providerList.push({
                        value: e.id,
                        label: e.name,
                    });
                });
                this.setState({
                    providerList,
                });
            },
            message => {
                if (!message) {
                    router.push('/auth/login');
                } else {
                    notificationSystem.addNotification({
                        title: 'Gagal mendapatkan data',
                        message: '',
                        level: 'error',
                    });
                }
            }
        );
    }

    changeEventProvider(val) {
        const provider = [];
        val.forEach(v => provider.push(v.value));

        this.setState({
            provider,
            page: 1 
        });
    }

    changeFiltterStatusHandler(index, value) {
        const { filterStatus } = this.state;
        const newStatus = update(filterStatus, {
            [index]: { value: { $set: value } },
        });

        this.setState({ filterStatus: newStatus });
    }

    render() {
        const {
            checkAll,
            totalCheck,
            idDetail,
            namaUsaha,
            outlet,
            cashNonTunai,
            switchFilter,
            bank,
            merchant,
            bukuKas,
            mdr,
            customer,
            buttonChecklist,
            confirmText,
            provider,
            status,
            resultStatusWallet,
            providerList,
            filterStatus,
        } = this.state;

        return (
            <div>
                <Table
                    ref={c => {
                        this.table = c;
                    }}
                    columns={tableMetaWithParam({
                        isCheckAll: checkAll,
                        checkAllHandler: this.checkAllHandler,
                        contents: this.state.content,
                        checkHandler: this.checkHandler,
                    })}
                    rowEvent={this.callActionHandler}
                    withWrapperRender={({
                        makeTable,
                        InputSearch,
                        PageSize,
                    }) => (
                        <section className="panel">
                            <div className="panel-heading">
                                <div className="row">
                                    <div className="col-md-3">
                                        <h6 className="panel-title">
                                            Non Cash Setting
                                        </h6>
                                    </div>
                                    <div
                                        className="col-md-2"
                                        style={{
                                            float: 'right',
                                            width: '125px',
                                        }}
                                    >
                                        <PageSize />
                                    </div>
                                    <div
                                        className="col-md-3"
                                        style={{
                                            paddingRight: '0px',
                                            float: 'right',
                                        }}
                                    >
                                        <InputSearch />
                                    </div>
                                </div>
                            </div>
                            <div className="panel-heading table-header">
                                <div className="row">
                                    <div className="col-md-12">
                                        <div style={{ width: '100%' }}>
                                            <div className="row">
                                                <div className="col-md-5">
                                                    <SelectStatusNonCash
                                                        data={dataStatusWallet}
                                                        value={resultStatusWallet}
                                                        onChangeHandler={val => this.changeSelectStatusUpdate(val)}
                                                        rowsData={totalCheck}
                                                        onSaveHandler={() => this.showPopupChangeStatus()}
                                                        disabled={buttonChecklist}
                                                    />
                                                </div>
                                                <div className="col-md-4" style={{position: 'relative', zIndex: 10}}>
                                                    <SelectMultiple
                                                        options={providerList}
                                                        selector="label"
                                                        value={provider}
                                                        changeEvent={val => this.changeEventProvider(val)}
                                                        placeholder="Pilih Provider"
                                                        style={{ color: 'red' }}
                                                    />
                                                </div>
                                                <div
                                                    className="col-md-3"
                                                    style={{
                                                        float: 'right',
                                                        textAlign: 'right',
                                                    }}
                                                >
                                                    <SwitchBoxMultiple
                                                        dataset={filterStatus}
                                                        onChangeEvent={(index, value) => this.changeFiltterStatusHandler(index, value)}
                                                        widthFull
                                                        widthFullButton
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="panel-body">{makeTable()}</div>
                        </section>
                    )}
                    // server-side
                    onFetch={this.fetchDataHandler}
                />

                <SidePopup
                    ref={c => {
                        this.sidePop = c;
                    }}
                    width={650}
                    saveHandle={() => this.saveHandler(idDetail)}
                >
                    <h4 className="side-popup-title">
                        Detail Pembayaran Non-Tunai
                    </h4>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Nama Usaha"
                                placeholder="Nama Usaha"
                                value={namaUsaha}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Pilih Outlet"
                                placeholder="Pilih Outlet"
                                value={outlet}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-6">Status</div>
                        <div
                            className="col-sm-6"
                            style={{ textAlign: 'right' }}
                        >
                            <Switch
                                checked={status}
                                changeEvent={value =>
                                    this.setState({
                                        status: value,
                                    })
                                }
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Nama Pembayaran Non Tunai *"
                                placeholder="Nama Pembayaran Non Tunai *"
                                value={cashNonTunai}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">Jenis *</div>
                        <div className="col-sm-12">
                            <SwitchBox
                                dataset={SwitchData}
                                value={switchFilter}
                                changeEvent={() => {}}
                                fullWidthButton
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Provider / Bank *"
                                placeholder="Provider / Bank *"
                                value={bank}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Kode Merchant"
                                placeholder="Kode Merchant"
                                value={merchant}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputText
                                label="Settlement Buku Kas"
                                placeholder="Settlement Buku Kas"
                                value={bukuKas}
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputPercent
                                label="MDR (%)"
                                value={mdr}
                                name="mdr"
                                desimal
                                disabled
                            />
                        </div>
                    </div>
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            <InputPercent
                                label="Biaya Tambahan Customer %"
                                value={customer}
                                name="customer"
                                desimal
                                disabled
                            />
                        </div>
                    </div>
                </SidePopup>
                <ModalPopup
                    title="Konfirmasi"
                    width={560}
                    confirmHandle={val => {
                        this.updateStatus(val);
                    }}
                    confirmText={confirmText ? 'Aktifkan' : 'Non-aktifkan'}
                    type={confirmText ? 'confirm' : 'delete'}
                    ref={c => {
                        this.actionPopup = c;
                    }}
                >
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            Apakah anda yakin ingin{' '}
                            <b>
                                {confirmText
                                    ? 'Mengaktifkan'
                                    : 'Menon-aktifkan'}
                            </b>{' '}
                            wallet dari <b>{totalCheck}</b> outlet
                        </div>
                    </div>
                </ModalPopup>
            </div>
        );
    }
}

export default CoreHOC(NoCashSetting);

import NumberDateTimeColumn from '../../../../components/table/components/NumberDateTimeColumn';
import StatusApproval from '../../../../components/table/components/StatusApproval';


const errorMessages = {
    edcName: 'Nama EDC harus diisi',
    idAkunting: 'Settlement Buku Kas harus diisi',
    bankName: 'Nama bank harus diisi',
    rekeningType: 'Jenis rekening harus diisi',
    businessName: 'Nama usaha harus diisi',
    companyAddress: 'Alamat Usaha harus diisi',
    npwpInput: 'NPWP harus diisi',
    ownerFalseFormatNPWP: 'NPWP harus 15 digit angka',
    businessProvince: 'Provinsi harus diisi',
    companyCity: 'Kota harus diisi',
    companyPostalCode: 'Kode pos harus diisi',
    merchantPhone: 'Telp / HP harus diisi',
    businessType: 'Jenis usaha harus diisi',
    ownerName: 'Nama pemilik usaha harus diisi',
    ownerAddress: '<PERSON>amat pemilik harus diisi',
    ownerPhone: 'No telepon pemilik harus diisi',
    ownerHandphone: 'No handphone pemilik harus diisi',
    merchantEmail: 'Email harus diisi',
    nblBankName: 'Nama bank harus diisi',
    nblSinceYear: 'Sejak tahun harus diisi',
};

const fileUploadService = 'wallet';

const TABLE_META = [
  {
    Header: 'Business Name',
    accessor: 'nama_usaha',
  },
  {
    Header: 'Provider',
    accessor: 'nama_bank',
  },
  {
    Header: 'Outlet',
    accessor: 'cabang_name',
    titleStyles: { width: '12%' },
  },
  {
    Header: 'Status',
    accessor: 'status',
    titleStyles: { width: '15%' },
    Cell: StatusApproval,
  },
  {
    Header: 'Create Date',
    accessor: 'createdate',
    Cell: NumberDateTimeColumn,
  },
  {
    Header: 'Update Date',
    accessor: 'updatedate',
    Cell: NumberDateTimeColumn,
  },
  {
    Header: 'Keterangan',
    accessor: 'notes',
  },
];

const uploadUrl = 'edc_submission/upload_berkas';

export {
  TABLE_META, errorMessages, fileUploadService, uploadUrl,
};

import React from 'react'

import CheckHeaderColumn from '../../../../components/table/components/CheckHeaderColumn';

const CheckboxRow = ({ data, contents, checkHandler }) => {
  const { id } = data;

  const contentIdx = contents.findIndex(content => content === id);

  return (
      <div onClick={(event) => {
        event.stopPropagation();
        checkHandler(id)
      }}>
          <div className="checkbox-custom checkbox-default">
              <input
                  type="checkbox"
                  id={`checkItem${id}`}
                  checked={contentIdx > -1}
                  readOnly
              />
              <label htmlFor={id} />
          </div>
      </div>
  );
}

const tableMetaWithParam = ({
  isCheckAll,
  checkAllHandler,
  contents,
  checkHandler,
}) => [
  {   
      Header: () => (
          <CheckHeaderColumn
              isCheckAll={isCheckAll}
              headerCheckFunc={checkAllHandler}
          />
      ),
      accessor: 'check',
      sortable: false,
      locked: false,
      visible: true,
      width: 50,
      Cell: ({ original }) => (
          <CheckboxRow data={original} contents={contents} checkHandler={checkHandler} />
      ),
  },
  {
    Header: '<PERSON>a <PERSON>aha',
    accessor: 'usaha_name',
  },
  {
    Header: 'Email Outlet',
    accessor: 'usaha_email',
  },
  {
    Header: 'Nama Outlet',
    accessor: 'outlet_name',
  },
  {
    Header: 'Provider',
    accessor: 'provider_name',
  },
  {
    Header: 'status',
    accessor: 'status',
    Cell: ({ original }) => +original.status === 1 ? (
      <div className="wallet-status-label" style={{ backgroundColor: '#66D4D3' }}>{'AKTIF'}</div>
    ) : (
      <div className="wallet-status-label" style={{ backgroundColor: '#C4C4C4' }}>{'NONAKTIF'}</div>
    )
  },
];

const SwitchData = [
  {
      text: 'MESIN EDC',
      value: '1',
      disabled: true,
  },
  {
      text: 'TRANSFER',
      value: '2',
      disabled: true,
  },
  {
      text: 'WALLET QR SNAP DYNAMIC',
      value: '3',
      disabled: false,
  },
];

const dataStatusWallet = [
  {
      value: '1',
      text: 'Aktifkan Pembayaran',
  },
  {
      value: '0',
      text: 'Non-aktifkan Pembayaran',
  },
];


export { tableMetaWithParam, SwitchData, dataStatusWallet };

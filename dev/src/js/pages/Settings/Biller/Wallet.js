import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';

import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/table/v.2/Table';
import Select from '../../../components/form/Select';

import * as walletApi from '../../../data/wallet';
import { SUB_TYPE_SUBMISSIONS } from '../../../enum/submissionType';

import { tableMeta, statusFilterList, qrisTypeOption, channelOption } from './config/wallet';

import { catchError } from '../../../utils/helper';
@CoreHOC
export default class Wallet extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
  }

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    assignCalendar: null,
    assignButtons: null,
    router: {
      push: null,
    },
    notificationSystem: ({
      addNotification: () => {
        // do nothing
      },
    }),
  }

  constructor(props) {
    super(props);
    const { calendar } = this.props;
    this.state = {
      calendar,
      statusFilter: '0',
      providerFilter: '0',
      qrisFilter: '',
      channelFilter: '',
      providerFilterList: [],
    };
  }

  componentWillMount() {
    const {
      assignCalendar, assignButtons,
    } = this.props;

    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    
    assignButtons([
      {
        type: 'primary',
        content: (
          <span>
            <i className="fa fa-file-download-white" />
            {' '}
            Download Data
          </span>
        ),
        action: () => {
          this.downloadData();
        },
      },
      {
        type: 'primary',
        content: (
          <span>
            <i className="fa fa-plus" />
            {' '}
            Tambah Pengajuan Wallet
          </span>
        ),
        action: () => {
          this.callCreateHandler();
        },
      },
    ]);

    this.getWalletList();
  }

  downloadData = async () => {
    const { notificationSystem } = this.props;

    const { calendar, statusFilter, providerFilter, qrisFilter, channelFilter } = this.state;

    if (calendar.start === '' && calendar.end === '') {
      notificationSystem.addNotification({
          level: 'error',
          title: 'Download data failed',
          message: 'Please use date filter to avoid data overload',
      });
      return;
    }

    if (this.table && this.table.state.fakeContent.length < 1) {
      notificationSystem.addNotification({
          level: 'error',
          title: 'Download data failed',
      message: 'Download data cannot be empty',
      });
      return;
    }

    let payload = {
      start_date: moment(calendar.start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end_date: moment(calendar.end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      status: +statusFilter,
      type: +SUB_TYPE_SUBMISSIONS.WALLET,
    };

    // check if providerFilter is selected
    if (+providerFilter !== 0) {
      payload = {
        ...payload,
        provider_id: +providerFilter,
      };
    }

    // check if search filter is not empty
    if (this.table.state.filterAll) {
      payload = {
        ...payload,
        search: this.table.state.filterAll,
      };
    }

    // get qr_type params
    if (qrisFilter) {
      payload = { ...payload, ...{ qr_type: qrisFilter } };
    }

    if (channelFilter) payload = { ...payload, ...{ channel: channelFilter } };

    try {
      const res = await walletApi.downloadListviewWallet(payload);
      const { url } = res;
      if (url && url.length > 0) {
        url.forEach((linkDownload) => {
          window.open(linkDownload);
        });
      } else {
        throw new Error('Download URL not available');
      }
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Failed to get data',
        message: catchError(e),
        level: 'error',
      });
    }
  }

  callCreateHandler = () => {
    const { router } = this.props;
    router.push('/non-cash-setting/wallet-payment/create');
  }

  callEditDetailHandler = ({ _original: data }) => {
    window.open(`/non-cash-setting/wallet-payment/view/${data.submission_no}`, '_blank');
  }

  getWalletList = async () => {
    const { notificationSystem, showProgress, hideProgress } = this.props;

    try {
      showProgress();
      const res = await walletApi.getWallet();
      if (!res.status) throw new Error(res.msg);
      const { data } = res;

      const providerFilterList = data.map(x => ({ id: Number(x.id_bank), name: x.name }));

      providerFilterList.unshift({ id: 0, name: 'Semua Wallet' });

      this.setState({
        providerFilterList,
      });
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Failed to get Provider data',
        message: catchError(e),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  changeDateHandler = (startDate, endDate) => {
    const { assignCalendar, showProgress, hideProgress } = this.props;
    assignCalendar(startDate, endDate);
    this.setState({
      calendar: {
          start: startDate,
          end: endDate,
      },
    }, async () => {
      showProgress();
      await this.table.forceRefetch();
      hideProgress();
    });
  }

  fetchDataHandler = async (state) => {
    const { notificationSystem } = this.props;

    const {
        statusFilter, providerFilter, calendar, qrisFilter, channelFilter,
    } = this.state;

    const {
      page, pages, sorted, filtered, pageSize,
    } = state;

    let payload = {
      start_date: moment(calendar.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      end_date: moment(calendar.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      per_page: pageSize,
      page: parseInt(page, 10) + 1,
      limit: pageSize,
      status: statusFilter,
      type: SUB_TYPE_SUBMISSIONS.WALLET,
    };

    // get sort params
    if (sorted.length > 0) {
      const { [sorted.length - 1]: { id, desc } } = sorted;
      payload = { ...payload, ...{ order: id, sort: desc ? 'DESC' : 'ASC' } };
    }

    // get filter params
    if (filtered && filtered.length > 0) {
      const filterAll = filtered.find(data => data.id === 'all');
      payload = { ...payload, ...{ search: filterAll.value } };
    }

    // get provider_id params
    if (providerFilter && providerFilter !== '0') {
      payload = { ...payload, ...{ provider_id: providerFilter } };
    }

    // get qr_type params
    if (qrisFilter) {
      payload = { ...payload, ...{ qr_type: qrisFilter } };
    }

    if (channelFilter) payload = { ...payload, ...{ channel: channelFilter } };

    let err = null,
    retval = { data: [], pageCount: pages > -1 ? pages : -1 };

    try {
      const res = await walletApi.getMerchantList(payload);
      if (!res.status) throw new Error(res.msg);

      retval = { data: res.data, pageCount: Number(res.meta.last_page) };
    } catch (e) {
      err = e;
      notificationSystem.addNotification({
        title: 'Failed to get data',
        message: catchError(err),
        level: 'error',
      });
    }
    retval = { ...retval, ...{ err } };

    return retval;
  }


  changeStatusFilterListFilterHandler = (val) => {
    const { showProgress, hideProgress } = this.props;
    this.setState({ statusFilter: val }, async () => {
      showProgress();
      await this.table.forceRefetch();
      hideProgress();
    });
  }

  changeProviderFilterListHandler = (val) => {
    const { showProgress, hideProgress } = this.props;
    this.setState({ providerFilter: val }, async () => {
      showProgress();
      await this.table.forceRefetch();
      hideProgress();
    });
  }

  changeQrisTypeFilterHandler = (val) => this.setState({ qrisFilter: val }, () => this.table.forceRefetch());
  changeChannelFilterHandler = (val) => this.setState({ channelFilter: val }, () => this.table.forceRefetch());

  render() {
    const {
      statusFilter, providerFilter, providerFilterList, qrisFilter, channelFilter,
    } = this.state;
    return (
      <div>
        <Table
          ref={(c) => { this.table = c; }}
          columns={tableMeta}
          rowEvent={this.callEditDetailHandler}
          withWrapperRender={({
            makeTable, InputSearch, PageSize,
          }) => (
            <section className="panel">
              <div className="panel-heading table-header">
                <div className="row">
                  <div className="col-md-3">
                    <h4 className="panel-title" style={{ paddingTop: '8px' }}>Wallet Submission</h4>
                  </div>
                  <div className="col-md-9 d-flex" style={{ gap: '8px' }}>
                    <div style={{ flexGrow: 1 }}>
                      <Select
                        data={channelOption}
                        value={channelFilter}
                        changeEvent={this.changeChannelFilterHandler}
                      />
                    </div>
                    <div style={{ flexGrow: 1 }}>
                      <Select
                        data={qrisTypeOption}
                        value={qrisFilter}
                        changeEvent={this.changeQrisTypeFilterHandler}
                      />
                    </div>
                    {
                      providerFilterList.length === 0 || (
                        <div style={{ flexGrow: 1 }}>
                          <Select
                            data={providerFilterList}
                            value={providerFilter}
                            changeEvent={this.changeProviderFilterListHandler}
                          />
                        </div>
                      )
                    }
                    <div style={{ flexGrow: 1 }}>
                      <Select
                        data={statusFilterList}
                        value={statusFilter}
                        changeEvent={this.changeStatusFilterListFilterHandler}
                      />
                    </div>
                    <div style={{ width: 150 }}>
                      <InputSearch />
                    </div>
                    <div style={{ marginLeft: '-15px'}}>
                      <PageSize />
                    </div>
                  </div>
                </div>
              </div>
              <div className="panel-body">
                {makeTable()}
              </div>
            </section>
          )}

          // server-side
          onFetch={this.fetchDataHandler}
        />
      </div>
    );
  }
}

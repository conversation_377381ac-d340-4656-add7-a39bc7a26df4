import React from 'react';
import PropTypes from 'prop-types';
import Select from '../../../../components/form/Select';

const SelectStatusNonCash = ({
    data, value, onChange<PERSON>and<PERSON>, rowsData, onSave<PERSON><PERSON><PERSON>, disabled,
}) => {
    let style = { padding: '7px' };

    if (!disabled) {
        style = {
            padding: '7px',
            borderRadius: '2px',
            backgroundColor: '#004a49',
            color: '#7ea5a4',
            cursor: 'not-allowed',
        };
    }

    return (
        <div className="stack">
            <div className="form-stack inline-form">
                <div className="form-group">
                    <div className="form-wrap text-center" style={{ width: '60%' }}>
                        <div className="form-group">
                            <Select
                                data={data}
                                value={value}
                                changeEvent={onChangeHandler}
                                disabled={!disabled}
                            />
                        </div>
                    </div>
                    <div className="form-wrap text-center" style={{ width: '40%' }}>
                        <div className="form-group">
                            <button
                                className="btn btn-block"
                                style={style}
                                onClick={onSaveHandler}
                                disabled={!disabled}
                                type="button"
                            >
                                <b>
                                    Tetapkan
                                    {rowsData > 0 ? ` (${rowsData})` : ''}
                                </b>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

    SelectStatusNonCash.propTypes = {
        data: PropTypes.arrayOf(
            PropTypes.object,
        ).isRequired,
        value: PropTypes.string.isRequired,
        onChangeHandler: PropTypes.func.isRequired,
        rowsData: PropTypes.number,
        onSaveHandler: PropTypes.func.isRequired,
        disabled: PropTypes.bool,
    };

    SelectStatusNonCash.defaultProps = {
        rowsData: '',
        disabled: true,
    };

export default SelectStatusNonCash;

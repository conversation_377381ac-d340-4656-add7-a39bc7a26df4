import React from 'react';
import Table from '../../../../components/table/v.2/Table';
import { tableMetaHistory } from '../config/wallet';

class TableHistorySubmission extends React.Component {
    render() {
        const { onFetch } = this.props;
        return (
            <Table
                columns={tableMetaHistory}
                rowEvent={({ _original: { submission_no } }) => window.open(`/non-cash-setting/wallet-payment/view/${submission_no}`, '_blank')}
                withWrapperRender={({ makeTable }) => (
                    <section className="panel">
                        <div className="panel-heading table-header">
                            <div className="row">
                                <div className="col-md-12">
                                    <h4
                                        className="panel-title"
                                        style={{ paddingTop: '8px' }}
                                    >
                                        Submission History
                                    </h4>
                                </div> 
                            </div>
                        </div>
                        <div className="panel-body">{makeTable()}</div>
                    </section>
                )}
                // server-side
                onFetch={onFetch}
            />
        );
    }
}

export default TableHistorySubmission;

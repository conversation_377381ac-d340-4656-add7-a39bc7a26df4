import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';
import _ from 'lodash';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/table/v.2/Table';
import Select from '../../../components/form/Select';
import SelectMultiple from '../../../components/form/SelectMultiple';

import * as biller<PERSON>pi from '../../../data/biller';

import { tableMeta, statusFilterList, OrderResponseCode } from './config/reportVa';

import { catchError } from '../../../utils/helper';
import InputNumber from '../../../components/form/InputNumber';
import ModalPopup from '../../../components/modalpopup/Container';

@CoreHOC
export default class Wallet extends Component {
    static propTypes = {
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }),
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
    }

    static defaultProps = {
        calendar: {
            start: '',
            end: '',
        },
        assignCalendar: null,
        assignButtons: null,
        router: {
            push: null,
        },
        notificationSystem: ({
            addNotification: () => {
                // do nothing
            },
        }),
    }

    constructor(props) {
        super(props);
        const { calendar } = this.props;
        this.state = {
            calendar,
            statusFilter: '',
            providerFilter: [],
            providerFilterList: [],
            amountFilter: undefined,
            popupMessage: '',
        };
        this.debounceSearch = _.debounce(this.refetchData.bind(this), 500);
    }

    componentWillMount() {
        const {
            assignCalendar, assignButtons,
        } = this.props;

        assignCalendar(null, null, (startDate, endDate) => {
            this.changeDateHandler(startDate, endDate);
        });
        assignButtons([]);

        this.getPaymentMethodList();
    }

    refetchData = async () => {        
        const { showProgress, hideProgress } = this.props;
        showProgress();
        await this.table.forceRefetch();
        hideProgress();
    }

    getPaymentMethodList = async () => {
        const { notificationSystem, showProgress, hideProgress } = this.props;

        try {
            showProgress();
            const res = await billerApi.getBillerPaymentMethod();
            if (!res.status) throw new Error(res.msg);
            const { data } = res;
            const providerFilterList = data.filter(x => x.type === 'va').map(x => ({ value: Number(x.id), label: x.additional_data.constant_provider }));

            // providerFilterList.unshift({ id: 0, name: 'Semua Bank' });

            this.setState({
                providerFilter: providerFilterList,
                providerFilterList,
            });
        } catch (e) {
        notificationSystem.addNotification({
            title: 'Failed to get Provider data',
            message: catchError(e),
            level: 'error',
        });
        } finally {
            hideProgress();
        }
    }

    changeDateHandler = (startDate, endDate) => {
        const { assignCalendar, showProgress, hideProgress } = this.props;
            assignCalendar(startDate, endDate);
        this.setState({
            calendar: {
                start: startDate,
                end: endDate,
            },
        }, this.refetchData);
    }

    fetchDataHandler = async (state) => {
        const { notificationSystem } = this.props;

        const {
            statusFilter, providerFilter, calendar, amountFilter,
        } = this.state;

        const {
            page, pages, sorted, filtered, pageSize,
        } = state;

        let payload = {
            start_date: moment(calendar.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            end_date: moment(calendar.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
            per_page: pageSize,
            page: parseInt(page, 10) + 1,
            limit: pageSize,
            ...(statusFilter && { status: statusFilter}),
            ...(providerFilter && providerFilter.length && { bank: providerFilter.map(x => x.value)}),
            ...(typeof amountFilter !== 'undefined' && { amount: amountFilter}),
        };
        
        if (sorted.length > 0) {
            const { [sorted.length - 1]: { id, desc } } = sorted;
            payload = { ...payload, ...{ order: id, sort: desc ? 'DESC' : 'ASC' } };
        }
        if (filtered && filtered.length > 0) {
            const filterAll = filtered.find(data => data.id === 'all');
            payload = { ...payload, ...{ search: filterAll.value } };
        }

        let err = null,
        retval = { data: [], pageCount: pages > -1 ? pages : -1 };

        try {
            const res = await billerApi.getReportVaList(payload);
            if (!res.status) throw new Error(res.msg);
            retval = { data: res.data, pageCount: Number(res.meta.last_page) };
        } catch (e) {
            err = e;
            notificationSystem.addNotification({
                title: 'Failed to get data',
                message: catchError(err),
                level: 'error',
            });
        }
        retval = { ...retval, ...{ err } };

        return retval;
    }

    changeStatusFilterListFilterHandler = (val) => {
        const { showProgress, hideProgress } = this.props;
        this.setState({ statusFilter: val }, this.refetchData);
    }

    changeProviderFilterListHandler = (val) => {
        const { showProgress, hideProgress } = this.props;
        this.setState({ providerFilter: val }, this.refetchData);
    }

    changeAmountFilterHandler = (val, e) => {
        if (!e) return;
        this.setState({ amountFilter: val });
        this.debounceSearch();
    }

    checkStatusHandle = async (val) => {
        const { notificationSystem, showProgress, hideProgress } = this.props;
        try {
            showProgress();
            const res = await billerApi.checkVaStatus(val);
            if (!res.data) throw new Error(res.status.message);
            this.setState({ popupMessage: OrderResponseCode[res.data.payment_status](res.data.transaction_no_nota)}, () => this.showPopup());
        } catch (e) {
            const err = e;
            notificationSystem.addNotification({
                title: 'Failed to check status transaction',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    showPopup = () => {
        this.cekStatusPopup.showPopup();
    }

    hidePopup = () => {
        const { showProgress, hideProgress } = this.props;
        this.cekStatusPopup.hidePopup();
        this.setState({ popupMessage: '' }, async () => {
            showProgress();
            await this.table.forceRefetch();
            hideProgress();
        });
    }

    render() {
        const {
            statusFilter, providerFilter, providerFilterList, amountFilter, popupMessage,
        } = this.state;
        return (
            <div>
                <Table
                    ref={(c) => { this.table = c; }}
                    columns={tableMeta(this.checkStatusHandle)}
                    rowEvent={this.callEditDetailHandler}
                    withWrapperRender={({
                        makeTable, InputSearch, PageSize,
                    }) => (
                        <section className="panel">
                        <div className="panel-heading table-header">
                            <div className="row">
                                <div className="col-md-12">
                                    <h4 className="panel-title" style={{ paddingTop: '8px' }}>Laporan Virtual Account</h4>
                                </div>
                            </div>
                        </div>
                        <div className="panel-heading table-header">
                            <div className="d-flex" style={{ justifyContent: 'space-between' }}>
                                <div className="row" style={{ width: '100%' }}>
                                    <div className="col-sm-2">
                                        <InputNumber
                                            placeholder="Amount"
                                            name="amount"
                                            value={amountFilter}
                                            changeEvent={this.changeAmountFilterHandler}
                                        />
                                    </div>
                                    {providerFilterList.length === 0 || (
                                        <div className="col-sm-5">                    
                                            <SelectMultiple
                                                options={providerFilterList}
                                                value={providerFilter}
                                                selector="label"
                                                changeEvent={this.changeProviderFilterListHandler}
                                            />
                                        </div>
                                    )}
                                    <div className="col-sm-2">
                                        <Select
                                            data={statusFilterList}
                                            value={statusFilter}
                                            changeEvent={this.changeStatusFilterListFilterHandler}
                                        />
                                    </div>
                                    <div className="col-sm-2">
                                        <InputSearch />
                                    </div>
                                    <div className="col-sm-1">
                                        <PageSize />
                                    </div>
                                </div>
                                {/* <div className="d-flex" style={{ gap: '8px' }}>
                                    <InputSearch />
                                    <PageSize />
                                </div> */}
                            </div>
                        </div>
                        <div className="panel-body">
                            {makeTable()}
                        </div>
                        </section>
                        )}
                    onFetch={this.fetchDataHandler}
                />
                <ModalPopup
                    type="assign"
                    classes="assign-Modal"
                    confirmText={'Oke'}
                    confirmHandle={this.hidePopup}
                    ref={(c) => { this.cekStatusPopup = c; }}
                    width={460}
                    hideCancelButton
                >
                    <h3 style={{ paddingBottom: 16, borderBottom: 'solid 1px #005252'}}>
                        Information
                    </h3>
                    <p>
                        {popupMessage}
                    </p>
                </ModalPopup>
            </div>
        );
    }
}

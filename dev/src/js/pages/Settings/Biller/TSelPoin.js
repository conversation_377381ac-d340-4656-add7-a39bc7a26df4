import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';

import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/table/v.2/Table';
import Select from '../../../components/form/Select';
import { catchError } from '../../../utils/helper';

import * as walletApi from '../../../data/wallet';
import { columns, statusFilterList } from './config/tselPoin';

import { SUB_TYPE_SUBMISSIONS } from '../../../enum/submissionType';

class TSelPoin extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
  }

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    assignCalendar: null,
    assignButtons: null,
    router: {
      push: null,
    },
    notificationSystem: ({
      addNotification: () => {
        // do nothing
      },
    }),
    showProgress: () => {
      // do nothing
    },
    hideProgress: () => {
      // do nothing
    },
  }

  constructor(props) {
    super(props);
    const { calendar } = this.props;
    this.state = {
      calendar,
      statusFilter: '0',
    };
  }

  componentWillMount() {
    const {
      assignCalendar, assignButtons,
    } = this.props;
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([{
      type: 'primary',
      content: <span>
        <i className="fa fa-plus" />
        {' '}
        Tambah Pengajuan Telkomsel Poin
               </span>,
      action: this.callCreateHandler,
    }]);
  }

  callCreateHandler = () => {
    const { router } = this.props;
    router.push('/non-cash-setting/tsel-poin/create');
  }

  callEditDetailHandler = ({ _original: data }) => {
    const { router } = this.props;
    router.push(`/non-cash-setting/tsel-poin/view/${data.submission_no}`);
  }

  changeDateHandler = (startDate, endDate) => {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);
    this.setState({
      calendar: {
          start: startDate,
          end: endDate,
      },
    }, () => {
      this.table.forceRefetch();
    });
  }

  fetchDataHandler = async (state) => {
    const {
      showProgress, hideProgress, notificationSystem,
    } = this.props;
    const {
      page, pages, filtered, pageSize, sorted,
    } = state;
    const { statusFilter, calendar } = this.state;

    let payload = {
      start_date: moment(calendar.start, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      end_date: moment(calendar.end, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      page: parseInt(page, 10) + 1,
      limit: pageSize,
      type: SUB_TYPE_SUBMISSIONS.TSEL_POIN,
      status: statusFilter,
    };

    // get filter params
    if (filtered && filtered.length > 0) {
      const filterAll = filtered.find(data => data.id === 'all');
      payload = { ...payload, ...{ search: filterAll.value } };
    }

    if (sorted.length > 0) {
      const { [sorted.length - 1]: { id, desc } } = sorted;

      Object.assign(payload, {
          sort: `${id} ${desc ? 'DESC' : 'ASC'}`,
      });
    }

    const err = null;
    let retval = { data: [], pageCount: pages > -1 ? pages : -1 };

    showProgress();

    try {
      const res = await walletApi.getMerchantList(payload);

      if (!res) throw new Error('Gagal Mendapatkan Data');
      retval = { data: res.data, pageCount: Number(res.meta.last_page) };
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Gagal Mendapatkan Data Akun',
        message: catchError(e),
        level: 'error',
      });
    }

    hideProgress();

    retval = Object.assign({}, retval, {
      err,
    });

    return retval;
  }

  changeStatusFilterListFilterHandler = (val) => {
    this.setState({ statusFilter: val }, () => {
      this.refetchTable();
    });
  }

  refetchTable = () => {
    this.table.forceRefetch();
  }

  render() {
    const { statusFilter } = this.state;

    return (
      <div>
        <Table
          ref={(c) => { this.table = c; }}
          columns={columns}
          rowEvent={this.callEditDetailHandler}
          withWrapperRender={({
            makeTable, InputSearch, PageSize,
          }) => (
              <section className="panel">
                <div className="panel-heading table-header">
                  <div className="row">
                    <div className="col-md-4">
                      <h4 className="title">Telkomsel Poin</h4>
                    </div>
                    <div className="col-md-8">
                        <div
                          style={{
                            position: 'absolute',
                            width: '165px',
                            right: '22em',
                          }}
                        >
                          <Select
                            data={statusFilterList}
                            value={statusFilter}
                            changeEvent={this.changeStatusFilterListFilterHandler}
                          />
                        </div>
                      <PageSize />
                      <div className="filter-container">
                        <InputSearch />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="panel-body">
                  {makeTable()}
                </div>
              </section>
            )}

          // server-side
          onFetch={this.fetchDataHandler}
        />
      </div>
    );
  }
}

export default CoreHOC(TSelPoin);

import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import moment from 'moment';
import { FieldFeedbacks, FieldFeedback } from 'react-form-with-constraints';

import CoreHOC from '../../../core/CoreHOC';

import InputUser from './components/InputUser';
import PopupTestingQR from './components/PopupTestingQR';
import TableHistory from './components/TableHistorySubmission';
import Select from '../../../components/form/Select';
import InputText from '../../../components/form/InputText';
import InputPercent from '../../../components/form/InputPercent';
import TextArea from '../../../components/form/Textarea';
import Autocomplete from '../../../components/form/Autocomplete';
import MultiTagWithPopup from '../../../components/form/MultiTagWithPopup';
import CalendarPick from '../../../components/form/CalendarPick';
import FormValidation from '../../../components/form/FormValidation';
import InfoHover from '../../../components/helper/InfoHover';
import InputCurrency from '../../../components/form/InputCurrency';
import WalletPaymentPopup from '../../../components/walletpaymentpopup/Container';
import FileUpload from '../../../components/form/FileUpload';
import DataNotFound from '../../../components/DataNotFound';

import * as walletApi from '../../../data/wallet';
import * as usersProfileApi from '../../../data/users/profile';
import * as userApi from '../../../data/users';
import * as supportApi from '../../../utils/data/support';
import { validateEmail, formatDate, catchError } from '../../../utils/helper';

import { FORM_TYPES } from '../../../enum/form';
import { STATUS_TYPE } from '../../../enum/status';

import {
    jenisPajakList, errorMessages, fileUploadService, ciraterialMerchant, settlementLimitList, APPLICANT_IDENTITY, // tableMetaDetail,
} from './config/wallet';
import {
    COMPANY_ENUM, PROVIDER, STATUS_ENUM, STATUS_QR_ENUM,
} from './config/enumProvider';

import verifiedStatusIcon from '../../../../assets/images/ic_baseline-verified-user.svg';
import PopupPrintQRXendit from './components/PopupPrintQRXendit';
import InputNumber from '../../../components/form/InputNumber';
import InputTextArea from '../../../components/form/InputTextArea';
import DataAddressOwner from './components/AddWallet/DataAddressOwner';
import DataAdmin from './components/AddWallet/DataAdmin';
import DataOutlet from './components/AddWallet/DataOutlet';
import DataOwner from './components/AddWallet/DataOwner';
import DataBusinessAccount from './components/AddWallet/DataBusinessAccount';
import { setFileUpload } from './config/utils';
import DuplicateConfirmation from './components/DuplicateConfirmation';
import ModalUploadQRISCode from './components/ModalUploadQRISCode';

class AddWallet extends Component {
    static propTypes = {
        router: PropTypes.shape({
            location: PropTypes.shape({
                pathname: PropTypes.string,
            }),
            replace: PropTypes.func,
        }).isRequired,
        assignCalendar: PropTypes.func.isRequired,
        assignButtons: PropTypes.func.isRequired,
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
        params: PropTypes.shape({}).isRequired,
        notificationSystem: PropTypes.shape({}),
        setGlobalMessage: PropTypes.func.isRequired,
    };

    static defaultProps = {
        notificationSystem: undefined,
    };

    initialButtonActions = [
        {
            id: '1',
            type: null,
            content: (
                <span>
                    Batal
                </span>
            ),
            action: () => this.gotoBasePath(),
            isDisabled: false,
        },
        {
            id: '2',
            type: 'primary',
            content: <span>Simpan Perubahan</span>,
            action: () => this.saveDataHandler('save'),
            isDisabled: false,
        },
    ];

    constructor(props) {
        super(props);

        this.state = {
            companyTypeList: [{ id: '1', name: 'Perseorangan' }, { id: '2', name: 'Badan Hukum' }],
            approvalStatusList: [{ id: '1', name: 'In Progress' }, { id: '4', name: 'Approved' }, { id: '2', name: 'Rejected' }],
            outletStatusList: [{ id: '1', name: 'Milik Sendiri' }, { id: '2', name: 'Sewa' }],
            providerFilterList: [],
            countryList: [],
            provinceList: [],
            cityList: [],
            cityListOutlet: [],
            districtListOutlet: [],
            branchList: [],
            userList: [],
            walletPaymentData: [],
            accountingList: [],
            // currentWalletTab: 'gopay',
            isEditDisabled: false,
            isSuspend: false,
            isApproved: false,
            isRejected: false,
            footerButtons: this.initialButtonActions,
            innerWidthScreen: window.innerWidth,
            isFormSubmitted: false,
            showingWalletPaymentPopup: false,
            showingPopupTestQR: false,
            walletProvider: '',
            akuntingList: [],
            walletPaymentId: '',
            idAkunting: '',
            qrUrl: '',
            qrCode: '',
            qrImage: '',
            orderNo: '',
            paymentStatus: '',
            isDisabledTestQR: false,
            isVerifiedStatus: false,
            merchantDataFound: true,
            showingPopupQRXendit: false,
            showConfirmationPopup: false,
            qrStaticXendit: {
                qrString: '',
                merchantId: '',
                outletId: '',
                merchantName: '',
            },
            generateMidQrStatic: {
                mid: '',
            },
            isDisableGenerateMid: true,
            isDisablePrintQRStatic: true,
            isDisableFieldMid: false,
            isHitXenditActivation: false,
            isDisableClickGenerateMid: true,
            form: {
                idUser: '',
                accountingName: '',
                outlets: [],
                companyType: '1',
                companyName: '',
                namaBadanUsaha: '',
                ownerName: '', // nama pemilik | nama direksi
                ownerAddress: '', // alamat pemilik | alamat direksi
                ownerNoKtp: '', // nomor ktp pemilik | nomor ktp direksi
                ownerNoTelp: '', // nomor telp pemilik | nomor telp direksi
                ownerEmail: '', // email pemilik | email direksi
                sellProduct: '', // barang yang dijual
                companyEmail: '',
                companyPhone: '',
                companyOutlet: '',
                companyAddress: '',
                companyProvince: '',
                companyCountry: '',
                companyCity: '',
                companySubdistrict: '',
                companyPostalCode: '',
                companyOutletStatus: '0',
                bankName: '',
                nomorRekening: '',
                bankAtasNama: '',
                bankBranch: '',
                fotoUsahaDepan: { filename: '', path: '', thumbnail: '' },
                fotoUsahaSamping: { filename: '', path: '', thumbnail: '' },
                fotoUsahaLain: { filename: '', path: '', thumbnail: '' },
                fotoButab: { filename: '', path: '', thumbnail: '' },
                fotoKtp: { filename: '', path: '', thumbnail: '' },
                fileNpwp: { filename: '', path: '', thumbnail: '' },
                fileAktaPendirian: { filename: '', path: '', thumbnail: '' },
                fileSkm: { filename: '', path: '', thumbnail: '' },
                fileSiup: { filename: '', path: '', thumbnail: '' },
                fileTdp: { filename: '', path: '', thumbnail: '' },
                fileFormPendaftaranMerchant: { filename: '', path: '', thumbnail: '' },
                approvalStatus: '1', // 4: Approved | 2: Rejected
                statusNote: '',
                masaBerlakuSewa: moment().format('DD/MM/YYYY'),
                mid: '',
                mdr: '0',
                noPks: '',
                idProvider: '13',
                userNameEmaill: '',
                taxType: 'Pengusaha Kena Pajak',
                criteria: 'Kecil <2.5M',
                businessLocation: '',
                detailBusinessLocation: '',
                linkGoogleMaps: '',
                settlementLimit: '1',
                noIzinUsaha: '',
                nibNumber: '',
                merchantEstablishmentPlace: '',
                merchantEstablishmentDate: moment().format('DD/MM/YYYY'),
                validityPeriode: '',
                picName: '',
                picTitle: '',
                picFinance: '',
                picFinanceEmail: '',
                picFinancePhoneNumber: '',
                price: 1,
                latitude: 0,
                longitude: 0,
                qr_type: 'dynamic',
                qr_string: '',
                merchant_gps_location_latitude: '',
                merchant_gps_location_longitude: '',
                user_usaha_name: '',
                business_info_address: '',
                business_info_bentuk_usaha: '',
                business_info_id_kota: '',
                business_info_id_provinsi: '',
                business_info_id_subdistrict: '',
                business_info_kodepos: '',
                business_info_lokasi_gps_latitude: '',
                business_info_lokasi_gps_longitude: '',
                merchant_account_bank_id: '',
                channel: 'cockpit',
                applicantType: APPLICANT_IDENTITY.OWNER,
                canDuplicate: false,
            },
            detailSelectedUser: null,
            isShowModalUploadQRISCode: false,
        };

        this.confirmationRef = React.createRef();
    }

    componentWillMount = () => {
        const { assignCalendar, assignButtons } = this.props;

        assignCalendar(null, null, null);
        assignButtons([]);
    };

    componentDidMount() {
        const { params } = this.props;

        if (params.type === FORM_TYPES.CREATE) {
            this.startFetchingDetailData();
        }
        window.addEventListener('resize', this.updateDimensions);
    }

    componentWillUnmount() {
        window.removeEventListener('resize', this.updateDimensions);
    }

    updateDimensions = () => {
        this.setState({ innerWidthScreen: window.innerWidth });
    };

    _fetchOutletData = async (idUser) => {
        const { notificationSystem } = this.props;

        let data = [];
        try {
            const payload = {
                merchant_id: idUser,
            };
            const res = await walletApi.getOutletList(payload);

            if (!res.status) throw new Error(res.msg);

            ({ data } = res);
            data = data.map(x => ({ id: x.id_outlet, name: x.outlet_name }));
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data Outlet',
                message: catchError(err),
                level: 'error',
            });
        }

        return data;
    };

    startFetchingDetailData = async () => {
        const { showProgress, hideProgress } = this.props;
        showProgress();
        await this.setDetailData();
        hideProgress();
    };

    setDetailData = async () => {
        const { params } = this.props;
        const fetchWilayahData = new this._fetchWilayahData();

        const { countryList, provinceList } = await fetchWilayahData;

        if (params.type === FORM_TYPES.CREATE) {
            this.setState(({ form: prevForm }) => ({
                isEditDisabled: params.id === FORM_TYPES.CREATE,
                countryList,
                provinceList,
                branchList: [],
                form: update(prevForm, {
                    companyCountry: { $set: '107' },
                    id: { $set: '' },
                    approvalStatus: { $set: '1' },
                }),
            }));
        } else {
            let approvalStatusList = [{ id: '4', name: 'Approved' }, { id: '2', name: 'Rejected' }];
            const fetchDetailData = new this._fetchDetailData(params.id);
           
            const merchantDetail = await fetchDetailData;

            if (!merchantDetail) {
                this.setState({
                    merchantDataFound: false,
                }, () => {
                    this.updateButtonsState(true, true);
                });
            } else {
                const { merchant_id: idUser, merchant_email: userMerchant, status: statusWallet } = merchantDetail;

                const fetchOutletData = new this._fetchOutletData(idUser);
                const fetchUserList = new this._onFetchUser({ page: 0, filterText: userMerchant });

                if (String(statusWallet) === STATUS_ENUM.IN_PROGRESS) {
                    approvalStatusList = [{ id: '1', name: 'In Progress' }, { id: '4', name: 'Approved' }, { id: '2', name: 'Rejected' }];
                    this.setState({ isDisabledTestQR: true });
                } else if (String(statusWallet) === STATUS_ENUM.APPROVE) {
                    this.setState({ isDisabledTestQR: false });
                }

                const branchList = await fetchOutletData;
                const userList = await fetchUserList;
                // const akuntingList = await fetchAkuntingList;
                this.setWalletList(merchantDetail.wallets);
                this._fetchAccountingList(merchantDetail.outlets.map(x => ({ id: x })));
                this.setState(
                    {
                        isEditDisabled: params.type === FORM_TYPES.VIEW,
                        countryList,
                        provinceList,
                        branchList,
                        userList,
                        approvalStatusList,
                    },
                    () => {
                        const cities = this._fetchCityData(
                            merchantDetail.business_info.id_provinsi,

                        );
                        const districts = this._fetchDistrictData(
                            merchantDetail.business_info.id_provinsi,
                            merchantDetail.business_info.id_kota,

                        );
                        let citiesOutlet = [];
                        let districtsOutlet = [];
                        if (merchantDetail.business_info) {
                            citiesOutlet = this._fetchCityData(
                                merchantDetail.merchant_province_id,
                            );
                            districtsOutlet = this._fetchDistrictData(
                                merchantDetail.merchant_province_id,
                                merchantDetail.merchant_city_id,
                            );
                        }
                        this.setInitFormData(
                            merchantDetail,
                            countryList,
                            provinceList,
                            cities,
                            districts,
                            citiesOutlet,
                            districtsOutlet,
                        );
                    },
                );
            }
        }
    };

    _fetchWalletList = async (merchantId) => {
        const { notificationSystem } = this.props;

        const payload = {
            merchant_id: merchantId,
        };

        try {
            const res = await walletApi.getWalletDefaultList(payload);
            if (!res.status) throw new Error(res.msg);

            const dataProvider = res.data.map(x => ({
                value: x.bank_id, label: x.provider, bank_id: x.id_bank, ...x,
            }));

            this.setState({ providerFilterList: dataProvider.filter(x => x.bank_id !== PROVIDER.GO_PAY.id) });
        } catch (e) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data Provider',
                message: catchError(e),
                level: 'error',
            });
        }
    };

    setWalletList = async (walletPaymentData) => {
        const { form } = this.state;

        let newForm = update(form, {
            mdr: { $set: '0' },
        });

        if (walletPaymentData.length > 0) {
            newForm = update(form, {
                mdr: { $set: walletPaymentData[0].mbr || '0' },
            });
        }

        const dataProvider = walletPaymentData.map(x => ({
            value: x.id_bank, label: x.provider, bank_id: x.id_bank, ...x,
        }));

        this.setState({
            form: newForm,
            providerFilterList: dataProvider.filter(x => Number(x.id_bank) !== Number(PROVIDER.GO_PAY.id)),
            walletPaymentData,
        });
    };

    setInitFormData = (
        merchantDetail,
        countries,
        provincies,
        cities,
        districts = [],
        citiesOutlet,
        districtsOutlet,
    ) => {
        const {
            id,
            business_type: companyType,
            merchant_name: companyName,
            nama_badan_usaha: namaBadanUsaha,
            owner_name: ownerName,
            address: ownerAddress,
            no_ktp: ownerNoKtp,
            phone_number: ownerNoTelp,
            owner_email: ownerEmail,
            product_type: sellProduct,
            merchant_email: companyEmail,
            merchant_phone_number: companyPhone,
            merchant_name: companyOutlet,
            merchant_address: companyAddress,
            merchant_province_id: companyProvince,
            merchant_city_id: companyCity,
            merchant_area: companySubdistrict,
            merchant_postal_code: companyPostalCode,
            business_place_ownership_status: companyOutletStatus,
            merchant_account_bank_name: bankName,
            merchant_account_bank_number: nomorRekening,
            merchant_account_behalf_name: bankAtasNama,
            merchant_account_bank_area: bankBranch,
            id_bank: bankId = '',
            merchant_account_img: fotoButab,
            berkas_form_pendaftaran: fileFormPendaftaranMerchant,
            masa_berlaku_sewa_du: masaBerlakuSewa,
            lokasi_gps_du: lokasiDu,
            submission_no: merchantBillerNo,
            status: approvalStatus,
            note: statusNote,
            outlets,
            mid,
            akunting_kode: akuntingKode,
            akunting_name: akuntingName,
            no_pks: noPks,
            wallet_id: idProvider,
            userCabang_user: idCabang,
            merchant_id: idUser,
            username_email: userNameEmaill,
            business_owner: businessOwner,
            business_legal: businessLegal,
            business_profile: businessProfile,
            minimum_limit_type: settlementLimit,
            business_pic: businessPic,
            qr_type: qrType,
            qr_string: qrString,
            user_usaha_name: userUsahaName,
            business_info: businessInfo = {
                address: '',
                business_info_bentuk_usaha: '',
                id_kota: '',
                id_provinsi: '',
                id_subdistrict: '',
                kodepos: '',
                latitude: '',
                longitude: '',
            },
            channel,
            identitas_pemohon: applicantType = APPLICANT_IDENTITY.PIC,
            is_can_duplicated,
        } = merchantDetail;

        let busniessInfoLocation = ['', ''];
        if (businessInfo && businessInfo.lokasi_gps) busniessInfoLocation = String(businessInfo.lokasi_gps).split(',');

        let locationGPS = ['', ''];
        if (lokasiDu) locationGPS = String(lokasiDu).split(',');

        const { ktp_img: fotoKtp, npwp_img: fileNpwp } = businessOwner;

        const {
            ministerial_decree_img: fileSkm,
            company_registration_certificate: fileTdp,
            deed_corporation: fileAktaPendirian,
            trade_license: fileSiup,
        } = businessLegal;

        const {
            tax_type: taxType,
            criteria,
            location: businessLocation,
            location_detail: detailBusinessLocation,
            link_google_maps: linkGoogleMaps,
            front_picture: fotoUsahaDepan,
            side_picture: fotoUsahaSamping,
            other_picture: fotoUsahaLain,
            register_number: noIzinUsaha,
            establishment_place: merchantEstablishmentPlace,
            establishment_date: merchantEstablishmentDate,
            validity_period: validityPeriode,
            nib_number: nibNumber = '',
        } = businessProfile;

        const {
            name: picName,
            title: picTitle,
            finance_email: picFinanceEmail,
            finance_phone_number: picFinancePhoneNumber,
            finance_name: picFinance,
        } = businessPic;

        const { branchList } = this.state;

        const newOutlets = outlets.map((x) => {
            const { id: branchId = '', name: branchName = '' } = branchList.find(i => Number(i.id) === Number(x)) || [];

            return { id: branchId, name: branchName };
        });

        let newFotoUsahaDepan = { filename: '', path: '', thumbnail: '' };

        if (fotoUsahaDepan && !Array.isArray(fotoUsahaDepan)) {
            newFotoUsahaDepan = { filename: fotoUsahaDepan.filename, path: fotoUsahaDepan.large, thumbnail: fotoUsahaDepan.thumbnail };
        }

        let newFotoUsahaSamping = { filename: '', path: '', thumbnail: '' };

        if (fotoUsahaSamping && !Array.isArray(fotoUsahaSamping)) {
            newFotoUsahaSamping = { filename: fotoUsahaSamping.filename, path: fotoUsahaSamping.large, thumbnail: fotoUsahaSamping.thumbnail };
        }

        let newFotoUsahaLain = { filename: '', path: '', thumbnail: '' };

        if (fotoUsahaLain && !Array.isArray(fotoUsahaLain)) {
            newFotoUsahaLain = { filename: fotoUsahaLain.filename, path: fotoUsahaLain.large, thumbnail: fotoUsahaLain.thumbnail };
        }

        let newFotoButab = { filename: '', path: '', thumbnail: '' };

        if (fotoButab && !Array.isArray(fotoButab)) {
            newFotoButab = { filename: fotoButab.filename, path: fotoButab.large, thumbnail: fotoButab.thumbnail };
        }

        let newFotoKtp = { filename: '', path: '', thumbnail: '' };

        if (fotoKtp && !Array.isArray(fotoKtp)) {
            newFotoKtp = { filename: fotoKtp.filename, path: fotoKtp.large, thumbnail: fotoKtp.thumbnail };
        }

        let newFileNpwp = { filename: '', path: '', thumbnail: '' };

        if (fileNpwp && !Array.isArray(fileNpwp)) {
            newFileNpwp = { filename: fileNpwp.filename, path: fileNpwp.large, thumbnail: fileNpwp.thumbnail };
        }

        let newFileAktaPendirian = { filename: '', path: '', thumbnail: '' };

        if (fileAktaPendirian && !Array.isArray(fileAktaPendirian)) {
            newFileAktaPendirian = { filename: fileAktaPendirian.filename, path: fileAktaPendirian.large, thumbnail: fileAktaPendirian.thumbnail };
        }

        let newFileSkm = { filename: '', path: '', thumbnail: '' };

        if (fileSkm && !Array.isArray(fileSkm)) {
            newFileSkm = { filename: fileSkm.filename, path: fileSkm.large, thumbnail: fileSkm.thumbnail };
        }

        let newFileSiup = { filename: '', path: '', thumbnail: '' };

        if (fileSiup && !Array.isArray(fileSiup)) {
            newFileSiup = { filename: fileSiup.filename, path: fileSiup.large, thumbnail: fileSiup.thumbnail };
        }

        let newFileTdp = { filename: '', path: '', thumbnail: '' };

        if (fileTdp && !Array.isArray(fileTdp)) {
            newFileTdp = { filename: fileTdp.filename, path: fileTdp.large, thumbnail: fileTdp.thumbnail };
        }

        let newFileFormPendaftaranMerchant = { filename: '', path: '', thumbnail: '' };

        if (
            fileFormPendaftaranMerchant
            && !Array.isArray(fileFormPendaftaranMerchant)
        ) {
            newFileFormPendaftaranMerchant = { filename: fileFormPendaftaranMerchant.filename, path: fileFormPendaftaranMerchant.large, thumbnail: fileFormPendaftaranMerchant.thumbnail };
        }

        const newApprovalStatus = String(approvalStatus);

        const isApproved = newApprovalStatus === STATUS_ENUM.APPROVE;

        if (isApproved) {
            const approvalStatusList = [{ id: '4', name: 'Approved' }, { id: '9', name: 'Suspend' }];
            this.setState({ approvalStatusList });
        }

        const isSuspend = String(newApprovalStatus) === STATUS_ENUM.SUSPEND;

        if (isSuspend) {
            const approvalStatusList = [{ id: '1', name: 'In Progress' }, { id: '4', name: 'Approved' }, { id: '9', name: 'Suspend' }];
            this.setState({ approvalStatusList });
        }

        const isRejected = String(newApprovalStatus) === STATUS_ENUM.REJECT;

        if (PROVIDER.XENDIT.id === idProvider && isApproved) {
            this.setState({
                isDisablePrintQRStatic: false, isDisabledTestQR: true, isHitXenditActivation: true, isDisableClickGenerateMid: false, isDisableGenerateMid: false,
            });
        }

        if (PROVIDER.XENDIT.id === idProvider) {
            this.setState({ isDisableFieldMid: true });
        } else {
            this.setState({ isDisableGenerateMid: true });
            if (isApproved) {
                this.setState({ isDisableClickGenerateMid: false });
            }
        }

        if ((PROVIDER.DANA.id === idProvider && isApproved) || PROVIDER.DANA_STATIC.id === idProvider) {
            this.setState({ isDisableFieldMid: true });
        }

        if (PROVIDER.DANA_STATIC.id === idProvider) {
            this.setState({ isDisabledTestQR: true });
            if (qrString) this.setState({ isDisablePrintQRStatic: false });
        }

        this.setState(({ form }) => ({
            isSuspend,
            isApproved,
            isRejected,
            countryList: countries,
            provinceList: provincies,
            cityListOutlet: citiesOutlet,
            cityList: cities,
            districtList: districts,
            districtListOutlet: districtsOutlet,
            form: update(form, {
                id: { $set: id },
                accountingName: { $set: `${akuntingKode} - ${akuntingName}` },
                mid: { $set: mid || '' },
                outlets: { $set: newOutlets },
                merchant_biller_no: { $set: merchantBillerNo || '' },
                companyType: { $set: companyType || '' },
                companyName: { $set: companyName || '' },
                namaBadanUsaha: { $set: namaBadanUsaha || '' }, // nama badan usaha
                ownerName: { $set: ownerName || '' }, // nama pemilik | nama direksi
                ownerAddress: { $set: ownerAddress || '' }, // alamat pemilik | alamat direksi
                ownerNoKtp: { $set: ownerNoKtp || '' }, // nomor ktp pemilik | nomor ktp direksi
                ownerNoTelp: { $set: ownerNoTelp || '' }, // nomor telp pemilik | nomor telp direksi
                ownerEmail: { $set: ownerEmail || '' }, // email pemilik | email direksi
                sellProduct: { $set: sellProduct || '' }, // barang yang dijual
                companyEmail: { $set: companyEmail || '' },
                companyPhone: { $set: companyPhone || '' },
                companyOutlet: { $set: companyOutlet || '' },
                companyAddress: { $set: companyAddress || '' },
                companyProvince: { $set: companyProvince || '' },
                companyCountry: { $set: '107' },
                companyCity: { $set: companyCity || '' },
                companySubdistrict: { $set: companySubdistrict || '' },
                companyPostalCode: { $set: companyPostalCode || '' },
                companyOutletStatus: { $set: companyOutletStatus || '' },
                bankName: { $set: bankName || '' },
                nomorRekening: { $set: nomorRekening || '' },
                bankAtasNama: { $set: bankAtasNama || '' },
                bankBranch: { $set: bankBranch || '' },
                fotoUsahaDepan: { $set: newFotoUsahaDepan },
                fotoUsahaSamping: { $set: newFotoUsahaSamping },
                fotoUsahaLain: { $set: newFotoUsahaLain },
                fotoButab: { $set: newFotoButab },
                fotoKtp: { $set: newFotoKtp },
                fileNpwp: { $set: newFileNpwp },
                fileAktaPendirian: { $set: newFileAktaPendirian },
                fileSkm: { $set: newFileSkm },
                fileSiup: { $set: newFileSiup },
                fileTdp: { $set: newFileTdp },
                fileFormPendaftaranMerchant: {
                    $set: newFileFormPendaftaranMerchant,
                },
                masaBerlakuSewa: {
                    $set: masaBerlakuSewa
                        ? moment(masaBerlakuSewa, 'YYYY-MM-DD').format(
                            'DD/MM/YYYY',
                        )
                        : moment().format('DD/MM/YYYY'),
                },
                approvalStatus: { $set: newApprovalStatus || '' },
                statusNote: { $set: statusNote || '' },
                idUser: { $set: idUser || '' },
                userNameEmaill: { $set: userNameEmaill || '' },
                noPks: { $set: noPks || '' },
                idProvider: { $set: idProvider || '13' },
                idCabang: { $set: idCabang || '-' },
                taxType: { $set: taxType || 'Pengusaha Kena Pajak' },
                criteria: { $set: criteria || 'Kecil <2.5M' },
                businessLocation: { $set: companyAddress || '' },
                detailBusinessLocation: { $set: detailBusinessLocation || '' },
                linkGoogleMaps: { $set: linkGoogleMaps || '' },
                settlementLimit: { $set: settlementLimit || '2' },
                noIzinUsaha: { $set: noIzinUsaha || '' },
                merchantEstablishmentPlace: {
                    $set: merchantEstablishmentPlace || '',
                },
                merchantEstablishmentDate: {
                    $set: merchantEstablishmentDate
                        ? moment(
                            merchantEstablishmentDate,
                            'YYYY-MM-DD',
                        ).format('DD/MM/YYYY')
                        : moment().format('DD/MM/YYYY'),
                },
                validityPeriode: { $set: validityPeriode || '' },
                picName: { $set: picName || '' },
                picTitle: { $set: picTitle || '' },
                picFinanceEmail: { $set: picFinanceEmail || '' },
                picFinancePhoneNumber: { $set: picFinancePhoneNumber || '' },
                picFinance: { $set: picFinance || '' },
                price: { $set: idProvider === PROVIDER.OVO.id ? 1000 : 1 },
                qr_type: { $set: qrType || '' },
                qr_string: { $set: qrString || '' },
                user_usaha_name: { $set: userUsahaName || '' },
                merchant_account_bank_id: { $set: bankId || '' },
                merchant_gps_location_latitude: { $set: locationGPS[0] },
                merchant_gps_location_longitude: { $set: locationGPS[1] },
                business_info_address: { $set: businessInfo ? businessInfo.address : '' },
                business_info_bentuk_usaha: { $set: businessInfo ? businessInfo.business_info_bentuk_usaha : '' },
                business_info_id_kota: { $set: businessInfo ? businessInfo.id_kota : '' },
                business_info_id_provinsi: { $set: businessInfo ? businessInfo.id_provinsi : '' },
                business_info_id_subdistrict: { $set: businessInfo ? businessInfo.id_subdistrict : '' },
                business_info_kodepos: { $set: businessInfo ? businessInfo.kodepos : '' },
                business_info_lokasi_gps_latitude: { $set: busniessInfoLocation[0] },
                business_info_lokasi_gps_longitude: { $set: busniessInfoLocation[1] },
                channel: { $set: channel || '' },
                applicantType: { $set: applicantType || APPLICANT_IDENTITY.PIC },
                nibNumber: { $set: nibNumber || '' },
                canDuplicate: { $set: is_can_duplicated || '' }
            }),
        }));
    };

    getAccountingOptions = data => data.map(item => ({ id: item.id_akunting, name: `${item.akunting_kode} - ${item.akunting_name}` }));

    // TODO: refactor this code
    gotoBasePath = () => {
        const { router } = this.props;
        router.push('/non-cash-setting/wallet-payment');
    };

    updateButtonsState = (isBtnCancelDisabled = false, isBtnSaveDisabled = false) => {
        const { walletSubmissionStatus } = this.state;
        const footerButtons = update(this.initialButtonActions, {
            0: { isDisabled: { $set: isBtnCancelDisabled } },
            1: { isDisabled: { $set: isBtnSaveDisabled } },
        });
        let buttons = footerButtons;
        const status = walletSubmissionStatus;

        // status approved
        if (parseInt(status, 10) === 4) {
            const [btnCancel] = footerButtons;
            buttons = [{ ...btnCancel }];
        }

        this.setState({ footerButtons: buttons });
    };

    saveDataHandler = async () => {
        const {
            form: { outlets: outletsState, idProvider }, isApproved, accountingList, userList,
        } = this.state;
        const {
            notificationSystem, hideProgress, setGlobalMessage, showProgress, params,
        } = this.props;

        this.updateButtonsState(true, true);

        const isFormValid = await this.form.validateForm();

        if (isFormValid) {
            // TODO: some logic / process here
            const { form, walletPaymentData, providerFilterList } = this.state;

            const wallets = providerFilterList
                .filter(x => Number(x.id_bank) === Number(form.idProvider))
                .map(item => ({ ...item, mdr: form.mdr }));

            const bankData = accountingList.find(x => x.id === form.merchant_account_bank_id) || {};

            let payloadOuter = {
                status: form.approvalStatus,
                mid: isApproved && [PROVIDER.XENDIT.id, PROVIDER.DANA.id, PROVIDER.DANA_STATIC.id].includes(idProvider) ? this.state.generateMidQrStatic.mid || form.mid : form.mid,
                mdr: form.mdr,
                keterangan: form.statusNote,
                is_draft: 0,
                bentuk_badan_usaha: form.companyType,
                nama_badan_usaha: form.namaBadanUsaha,
                masa_berlaku_sewa_du: formatDate(
                    form.masaBerlakuSewa,
                    'yyyy-mm-dd',
                ),
                alamat: form.ownerAddress || form.business_info_address,
                no_ktp: form.ownerNoKtp,
                no_hp: form.ownerNoTelp,
                email: form.ownerEmail,
                nama_pemilik: form.ownerName,
                barang_jasa: form.sellProduct,
                nama_bank: form.bankName || bankData.name || '',
                no_rek_bank: form.nomorRekening || bankData.no || '',
                atas_nama_bank: form.bankAtasNama || bankData.holder || '',
                cabang_bank: form.bankBranch,
                id_bank: form.merchant_account_bank_id,
                foto_butab_bank: form.fotoButab.filename,
                email_du: form.companyEmail,
                no_hp_du: form.companyPhone,
                nama_du: form.companyOutlet,
                alamat_du: form.companyAddress,
                provinsi_du: form.companyProvince,
                negara_du: form.companyCountry,
                kota_du: form.companyCity,
                area_du: form.companySubdistrict,
                lokasi_gps_du: `${form.merchant_gps_location_latitude},${form.merchant_gps_location_longitude}`,
                kode_pos_du: form.companyPostalCode,
                status_lokasi_du: form.companyOutletStatus,
                foto_depan_du: form.fotoUsahaDepan.filename,
                foto_samping_du: form.fotoUsahaSamping.filename,
                foto_lain_du: form.fotoUsahaLain.filename,
                outlets: outletsState.map(x => x.id),
                foto_ktp: form.fotoKtp.filename,
                foto_npwp: form.fileNpwp.filename,
                berkas_form_pendaftaran: form.fileFormPendaftaranMerchant.filename,
                wallets,
                akta_pendirian: form.fileAktaPendirian.filename,
                skm: form.fileSkm.filename,
                siup: form.fileSiup.filename,
                tdp: form.fileTdp.filename,
                user_id: form.idUser,
                no_pks: form.noPks,
                is_pengajuan_wallet: 1,
                is_pengajuan_tselpoint: 0,
                is_pengajuan_grabfood: 0,
                minimum_limit: form.settlementLimit,
                business_profile: {
                    business_location_business: form.businessLocation,
                    business_location_business_detail:
                        form.detailBusinessLocation,
                    business_link_google_maps: form.linkGoogleMaps,
                    business_tax_type: form.taxType,
                    business_criteria: form.criteria,
                    business_establishment_place:
                        form.merchantEstablishmentPlace,
                    business_establishment_date: formatDate(
                        form.merchantEstablishmentDate,
                        'yyyy-mm-dd',
                    ),
                    business_register_number: form.noIzinUsaha,
                    business_validity_periode: form.validityPeriode,
                    business_nib_number: form.nibNumber,
                },
                business_pic: {
                    business_pic_name: form.picName,
                    business_pic_title: form.picTitle,
                    business_pic_finance_email: form.picFinanceEmail,
                    business_pic_finance_phone_number:
                        form.picFinancePhoneNumber,
                    business_pic_finance: form.picFinance,
                },
                business_legal: {
                    legal_deed_corporation: form.fileAktaPendirian.filename,
                    legal_ministerial_decree: form.fileSkm.filename,
                },
                qr_type: form.qr_type,
                business_info: {
                    address: form.business_info_address,
                    id_kota: form.business_info_id_kota,
                    id_provinsi: form.business_info_id_provinsi,
                    id_subdistrict: form.business_info_id_subdistrict,
                    kodepos: form.business_info_kodepos,
                    lokasi_gps: `${form.business_info_lokasi_gps_latitude},${form.business_info_lokasi_gps_longitude}`,
                },
                identitas_pemohon: form.applicantType,
            };

            showProgress();
            if (String(form.id).length > 0) {
                Object.assign(payloadOuter, { id: form.id, merchant_biller_no: form.merchant_biller_no });

                if (isApproved && idProvider === PROVIDER.XENDIT.id) {
                    const payload = {
                        outlet_id: Number(form.outlets[0].id),
                        merchant_email: form.companyEmail,
                        merchant_name: form.outlets[0].name,
                        merchant_address: form.companyAddress,
                        merchant_postcode: form.companyPostalCode,
                    };

                    walletApi.updateMerchantBiller(payloadOuter)
                        .then(async () => {
                            try {
                                const res = await walletApi.accountActivation(payload);
                                if (!res.status) throw new Error(res.msg || res);
                                setGlobalMessage({
                                    title: 'Simpan data Berhasil',
                                    level: 'success',
                                });
                                // this.gotoBasePath();
                                this.startFetchingDetailData();
                            } catch (err) {
                                notificationSystem.addNotification({
                                    title: 'Simpan data gagal',
                                    message: catchError(err),
                                    level: 'error',
                                });
                            } finally {
                                hideProgress();
                            }
                        }).catch((err) => {
                            hideProgress();
                            notificationSystem.addNotification({
                                title: 'Simpan data gagal',
                                message: catchError(err),
                                level: 'error',
                            });
                        });
                } else {
                    try {
                        const res = await walletApi.updateMerchantBiller(payloadOuter);
                        if (!res.status) throw new Error(res.msg || res);
                        setGlobalMessage({
                            title: 'Simpan data Berhasil',
                            level: 'success',
                        });
                        // this.gotoBasePath();
                        this.startFetchingDetailData();
                    } catch (err) {
                        notificationSystem.addNotification({
                            title: 'Simpan data gagal',
                            message: catchError(err),
                            level: 'error',
                        });
                    } finally {
                        hideProgress();
                    }
                }
            } else {
                try {
                    payloadOuter = { ...payloadOuter, channel: 'cockpit' };

                    const res = await walletApi.createMerchantBiller(payloadOuter);
                    if (!res.status) throw new Error(res.msg || res);
                    setGlobalMessage({
                        title: 'Simpan data Berhasil',
                        level: 'success',
                    });
                    // this.gotoBasePath();
                    this.startFetchingDetailData();
                } catch (err) {
                    notificationSystem.addNotification({
                        title: 'Simpan data gagal',
                        message: catchError(err),
                        level: 'error',
                    });
                } finally {
                    hideProgress();
                }
            }

            this.updateButtonsState();
        } else {
            this.updateButtonsState(false, true);
        }

        this.setState(
            {
                isFormSubmitted: true,
            },
            () => {
                this.changeOutletHandler(outletsState);
            },
        );
    };

    _fetchDetailData = async (id) => {
        const { notificationSystem } = this.props;
        let newData = false;
        try {
            const res = await walletApi.getMerchantDetail({ id });
            const { data, status, msg } = res;
            if (!status) throw new Error(msg);
            newData = data;
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Simpan data gagal',
                message: catchError(err),
                level: 'error',
            });
        }

        return newData;
    };

    _fetchWilayahData = async () => {
        const res = await supportApi.getUserWilayah();
        let countryList = [];
        let provinceList = [];

        if (res.status && res.data.length > 0) {
            countryList = res.data;
            provinceList = countryList[0].province;
        }

        return { countryList, provinceList };
    };

    _fetchAccountingList = async (outlets = []) => {
        const ids = outlets.map(x => x.id).join(',');
        if (ids.length === 0) return;

        const payload = {
            page: 1,
            limit: 1000,
            type: 'rekening',
            id_outlet: ids,
        };

        const { data = [] } = await usersProfileApi.getListSubmission(payload);
        const newData = data.map(x => ({
            id: x.id, name: x.bank_name, disabled: x.status_id === 112, no: x.account_no, holder: x.account_holder,
        }));

        this.setState({ accountingList: newData });
    }

    _fetchCityData = (idProvinsi) => {
        const { provinceList } = this.state;

        const found = provinceList.find(x => String(x.id) === String(idProvinsi));
        if (found) {
            return found.city;
        }

        return [];
    };

    _fetchDistrictData = (idProvinsi, idKota) => {
        const { provinceList } = this.state;

        const found = provinceList.find(x => String(x.id) === String(idProvinsi));
        if (found) {
            const citiesList = found.city;
            const foundDistrict = citiesList.find(x => String(x.id) === String(idKota));
            if (foundDistrict) return foundDistrict.district;
        }

        return [];
    }

    setupCityData = async (idProvinsi, type) => {
        const { form } = this.state;
        const cityList = this._fetchCityData(idProvinsi);

        const newForm = update(form, {
            ...type === 'companyProvince' && {
                business_info_id_kota: { $set: '' },
                business_info_id_subdistrict: { $set: '' },
            },
            ...type === 'business_info_id_provinsi' && {
                companyCity: { $set: '' },
                companySubdistrict: { $set: '' },
            },
        });

        this.setState({
            ...type === 'companyProvince' && {
                cityListOutlet: cityList,
            },
            ...type === 'business_info_id_provinsi' && {
                cityList,
            },
            form: newForm,
        });
    };

    setupDistrictData = async (idProvinsi, idKota, type) => {
        const { form } = this.state;
        const districtList = this._fetchDistrictData(idProvinsi, idKota);

        const newForm = update(form, {
            ...type === 'companyCity' && {
                business_info_id_subdistrict: { $set: '' },
            },
            ...type === 'business_info_id_kota' && {
                companySubdistrict: { $set: '' },
            },
        });

        this.setState({
            ...type === 'companyCity' && {
                districtListOutlet: districtList,
            },
            ...type === 'business_info_id_kota' && {
                districtList,
            },
            form: newForm,
        });
    };

    _onInputChangeValidate = ({ target }) => {
        this.form.validateInput(target);

        if (this.inputTimeout) {
            clearTimeout(this.inputTimeout);
        }

        this.inputTimeout = setTimeout(() => {
            if (this.form.simpleValidateForm()) {
                this.updateButtonsState();
            } else {
                this.updateButtonsState(false, true);
            }
        }, 300);
    };

    _changeInputHandler = async (type, val, e) => {
        const { form, detailSelectedUser } = this.state;

        const { isFormSubmitted } = this.state;

        if (type === 'idProvider' && [PROVIDER.XENDIT.id, PROVIDER.DANA.id, PROVIDER.DANA_STATIC.id].includes(String(val))) {
            this.setState({
                isDisableGenerateMid: false,
                isDisableFieldMid: true,
            });
        } else if (type === 'idProvider' && String(val)) {
            this.setState({
                isDisableGenerateMid: true,
                isDisableFieldMid: false,
            });
        }

        if (isFormSubmitted && e) {
            const onInputChangeValidate = new this._onInputChangeValidate(e);
            await onInputChangeValidate;
        }

        let newForm = update(form, {
            [type]: { $set: val },
        });

        if (type === 'idProvider' && String(val)) {
            newForm = update(newForm, {
                mid: { $set: '' },
            });

            this.setState({
                generateMidQrStatic: { mid: '' },
            });
        }

        if (type === 'companyPostalCode' || type === 'business_info_kodepos') {
            const isNumber = /^\d*$/.test(val);
            if (isNumber) {
                newForm = update(form, {
                    [type]: { $set: val },
                });
            } else {
                newForm = update(form, {
                    [type]: { $set: form[type] },
                });
            }
        } else if (
            type === 'merchant_gps_location_latitude'
            || type === 'merchant_gps_location_longitude'
            || type === 'business_info_lokasi_gps_latitude'
            || type === 'business_info_lokasi_gps_longitude'
        ) {
            const isValid = /^-?[0-9]*\.?[0-9]*$/.test(val);
            if (isValid) {
                newForm = update(form, {
                    [type]: { $set: val },
                });
            } else {
                newForm = update(form, {
                    [type]: { $set: form[type] },
                });
            }
        } else if (type === 'applicantType') {
            if (val === APPLICANT_IDENTITY.OWNER) {
                newForm = update(newForm, {
                    ownerName: { $set: detailSelectedUser.owner },
                    ownerNoKtp: { $set: detailSelectedUser.ktp_no },
                    ownerNoTelp: { $set: detailSelectedUser.notlp },
                    fotoKtp: { $set: setFileUpload(detailSelectedUser.ktp_image) },
                });
            } else if (val === APPLICANT_IDENTITY.PIC) {
                newForm = update(newForm, {
                    ownerName: { $set: '' },
                    ownerNoKtp: { $set: '' },
                    ownerNoTelp: { $set: '' },
                    fotoKtp: { $set: { filename: '', path: '', thumbnail: '' } },
                });
            }
        }

        this.setState(
            {
                form: newForm,
            },
            () => {
                if (type === 'statusNote') this._onInputChangeValidate({ target: this.hiddenStatusNote });
                if (type === 'companyProvince') {
                    this.setupCityData(val, type);
                    this._onInputChangeValidate({ target: this.companyProvinceHidden });
                }
                if (type === 'companyCity') {
                    this.setupDistrictData(form.companyProvince, val, type);
                    this._onInputChangeValidate({ target: this.companyCityHidden });
                }
                if (type === 'companySubdistrict') this._onInputChangeValidate({ target: this.companySubdistrictHidden });
                if (type === 'business_info_id_provinsi') {
                    this.setupCityData(val, type);
                    this._onInputChangeValidate({ target: this.business_info_id_provinsi_hidden });
                }
                if (type === 'business_info_id_kota') {
                    this.setupDistrictData(form.business_info_id_provinsi, val, type);
                    this._onInputChangeValidate({ target: this.business_info_id_kota_hidden });
                }
                if (type === 'business_info_id_subdistrict') this._onInputChangeValidate({ target: this.business_info_id_subdistrict_hidden });
                if (type === 'fotoKtp') this._onInputChangeValidate({ target: this.refFotoKtp });
                if (type === 'fileAktaPendirian') this._onInputChangeValidate({ target: this.refFileAktaPendirian });
                if (type === 'fileSkm') this._onInputChangeValidate({ target: this.refFileSkm });
                if (type === 'fileSiup') this._onInputChangeValidate({ target: this.refFileSiup });
                if (type === 'fileTdp') this._onInputChangeValidate({ target: this.refFileTdp });
            },
        );
    };

    changeNumberValueHandler = async (type, val, e, phoneNumber = false) => {
        let allowToUpdate = false;
        const { form, isFormSubmitted } = this.state;
        const isPhoneNumber = /^\+\d*$/.test(val);
        const isNumber = /^\d*$/.test(val);

        if ((phoneNumber && isPhoneNumber) || isNumber) {
            allowToUpdate = true;
        }

        if (allowToUpdate) {
            if (isFormSubmitted && e) {
                const onInputChangeValidate = new this._onInputChangeValidate(
                    e,
                );
                await onInputChangeValidate;
            }
            const newForm = update(form, {
                [type]: { $set: val },
            });
            this.setState({ form: newForm });
        }
    };

    downloadEvent = (val) => {
        if (val) {
            window.open(val);
        }
    };

    walletTabContent = () => (
        <div>
            <h4 className="mb-sm">Dokumen Wajib</h4>
            <div className="label-title mb-xs">Perseorangan</div>
            <div className="mb-sm">
                <ul>
                    <li>
                        Kartu Tanda Penduduk (KTP) atau KITAS
                        {' '}
                        <span className="text-red">*</span>
                    </li>
                    <li>Nomor Pokok Wajib Pajak (NPWP)</li>
                    <li>Form pendaftaran Merchant</li>
                </ul>
            </div>
            <div className="label-title mb-xs">Badan Hukum</div>
            <div className="mb-sm">
                <ul>
                    <li>
                        Kartu Tanda Penduduk (KTP) atau KITAS Direksi
                        {' '}
                        <span className="text-red">*</span>
                    </li>
                    <li>
                        Nomor Pokok Wajib Pajak (NPWP) Badan Hukum
                        {' '}
                        <span className="text-red">*</span>
                    </li>
                    <li>
                        Akta Pendirian Usaha, serta perubahannya
                        {' '}
                        <span className="text-red">*</span>
                    </li>
                    <li>
                        Surat Keputusan Menteri Hukum dan HAM
                        {' '}
                        <span className="text-red">*</span>
                    </li>
                    <li>
                        Surat Ijin Usaha Perdagangan (SIUP) atau sejenisnya
                        {' '}
                        <span className="text-red">*</span>
                    </li>
                    <li>
                        Tanda Daftar Sekarang Perusahaan (TDP)
                        {' '}
                        <span className="text-red">*</span>
                    </li>
                    <li>Form pendaftaran Merchant</li>
                </ul>
            </div>
            <div>
                Dokumen wajib dapat dikirimkan ke PT. Majoo Teknologi Indonesia :
            </div>
            <div>
                PT. Majoo Teknologi Indonesia
                <br />
                Equity Tower, Lantai 26E
                <br />
                Jl. Jend. Sudirman, Kav. 52-53
                <br />
                Jakarta Selatan, 12190
            </div>
        </div>
    );

    notifyUploadFailed = (err) => {
        const { notificationSystem } = this.props;
        notificationSystem.addNotification({
            title: 'Upload file gagal',
            message: err,
            level: 'error',
            autoDismiss: 10,
        });
    };

    downloadPendaftaranMerchant = () => {
        window.open(
            'https://do-mayang.mangkujagat.com/mayang/uploads/wallet/docs/Form_Pendaftaran_Merchant-GOPAY.docx',
            '_blank',
        );
    };

    _onFetchUser = async (state) => {
        const { notificationSystem } = this.props;
        const { userList } = this.state;
        const { page, filterText, id } = state;

        if (filterText && filterText.length > 0 && filterText.length < 3) return userList;

        const payload = { limit: 5, page: page + 1 };

        // get filter params
        if (filterText && filterText.length > 0) {
            Object.assign(payload, { search: filterText });
        }

        let newData = [];

        try {
            if (!id) {
                const res = await walletApi.getUserList(payload);

                if (!res.status) throw new Error(res.msg);
                newData = res.data.map(x => ({
                    id: x.id,
                    name: x.merchant_name,
                    email: x.user_email,
                }));
            }
        } catch (message) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan gagal',
                message,
                level: 'error',
            });
        }

        this.setState({
            userList: newData,
        });

        return newData;
    };

    changeOutletHandler = (val) => {
        const { form } = this.state;
        const newForm = update(form, {
            outlets: { $set: val },
            user_usaha_name: { $set: val.length ? val.map(x => x.name).join(', ') : '' },
        });

        this.setState({
            form: newForm,
        }, async () => {
            this.validateOutlet();
            this._fetchAccountingList(val);
        });
    };


    validateOutlet = async () => {
        const { isFormSubmitted } = this.state;

        if (isFormSubmitted) {
            const onInputChangeValidate = new this._onInputChangeValidate({
                target: this.hiddenOutlet,
            });
            await onInputChangeValidate;
        }
    };

    fetchDetailMerchant = async (id) => {
        let result = {
            name: '', noktp: '', phone: '', ktp: { filename: '', path: '', thumbnail: '' },
        };
        if (!id) return result;
        const { showProgress, hideProgress, notificationSystem } = this.props;

        showProgress();
        try {
            const { data } = await userApi.getDetailUser({ id_user: id });

            if (Array.isArray(data) && data.length > 0) {
                this.setState({ detailSelectedUser: data[0] });
                result = {
                    name: data[0].owner,
                    noktp: data[0].ktp_no,
                    phone: data[0].notlp,
                    ktp: setFileUpload(data[0].ktp_image),
                };
            }
        } catch (message) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan gagal',
                message,
                level: 'error',
            });
        } finally {
            hideProgress();
        }

        return result;
    }

    changeUserHandler = async (val) => {
        const { form, userList } = this.state;

        const dataCompany = userList.find(x => x.id === val);
        const dataDetailCompany = await this.fetchDetailMerchant(dataCompany.id);

        const newForm = update(form, {
            idUser: { $set: val },
            companyEmail: { $set: dataCompany ? dataCompany.email : '' },
            mid: { $set: '' },
            mdr: { $set: '0' },
            keterangan: { $set: '' },
            outlets: { $set: [] },
            fotoUsahaDepan: { $set: { filename: '', path: '', thumbnail: '' } },
            fotoUsahaSamping: {
                $set: { filename: '', path: '', thumbnail: '' },
            },
            fotoUsahaLain: { $set: { filename: '', path: '', thumbnail: '' } },
            fotoButab: { $set: { filename: '', path: '', thumbnail: '' } },
            fotoKtp: { $set: { ...dataDetailCompany.ktp } },
            fileNpwp: { $set: { filename: '', path: '', thumbnail: '' } },
            fileAktaPendirian: {
                $set: { filename: '', path: '', thumbnail: '' },
            },
            fileSkm: { $set: { filename: '', path: '', thumbnail: '' } },
            fileSiup: { $set: { filename: '', path: '', thumbnail: '' } },
            fileTdp: { $set: { filename: '', path: '', thumbnail: '' } },
            fileFormPendaftaranMerchant: {
                $set: { filename: '', path: '', thumbnail: '' },
            },
            ownerName: { $set: dataDetailCompany.name },
            ownerNoKtp: { $set: dataDetailCompany.noktp },
            ownerNoTelp: { $set: dataDetailCompany.phone },
        });

        this.setState(
            { form: newForm, walletPaymentData: [], branchList: [] },
            () => {
                if (val) {
                    this.setupUserData(val);
                } else {
                    this.validateIdUser();
                }
            },
        );
    };

    setupUserData = async (idUser) => {
        const { showProgress, hideProgress } = this.props;

        const fetchOutletData = new this._fetchOutletData(idUser);
        const fetchWalletList = new this._fetchWalletList(idUser);

        showProgress();

        const branchList = await fetchOutletData;
        await fetchWalletList;

        hideProgress();

        this.setState({ branchList }, () => {
            this.validateIdUser();
        });
    };

    saveWalletPaymentHandler = (val) => {
        const { walletPaymentData, walletPaymentId } = this.state;
        const findIndex = walletPaymentData.findIndex(x => String(x.bank_id) === String(walletPaymentId));

        const newData = update(walletPaymentData, {
            [findIndex]: {
                id_akunting: { $set: val },
                // accountingName: { $set: settlementBukuKas },
            },
        });

        this.setState({
            walletPaymentData: newData,
            showingWalletPaymentPopup: false,
        });
    };

    hideWalletPaymentPopup = () => {
        this.setState({ showingWalletPaymentPopup: false });
    };

    showWalletPaymentPopupHandler = (data) => {
        this.setState({
            walletPaymentId: data.bank_id,
            showingWalletPaymentPopup: true,
            walletProvider: data.provider,
        });
    };

    validateIdUser = async () => {
        const { isFormSubmitted } = this.state;

        if (isFormSubmitted) {
            const onInputChangeValidate = new this._onInputChangeValidate({
                target: this.hiddenIdUser,
            });
            await onInputChangeValidate;
        }
    };

    validateApprovalStatus = async () => {
        const { isFormSubmitted } = this.state;

        if (isFormSubmitted) {
            const onInputChangeValidate = new this._onInputChangeValidate({
                target: this.hiddenApprovalStatus,
            });
            await onInputChangeValidate;
        }
    };

    changeApprovalStatusHandler = (val) => {
        const { form } = this.state;
        let mid = '';

        if (form.idProvider === PROVIDER.SHOPEE_PAY.id) {
            // idProvider for ShoopePay
            let outletId = '';

            if (form.outlets.some(x => x.id === form.idCabang)) {
                outletId = form.idCabang;
            } else {
                outletId = form.outlets[0].id;
            }
            mid = `MERCHANT_${form.idUser}:STORE_${outletId}`;
        }

        const newForm = update(form, {
            approvalStatus: { $set: val },
            mid: { $set: mid },
            mdr: { $set: '0' },
            statusNote: { $set: '' },
        });

        const isRejected = String(val) === STATUS_ENUM.REJECT;
        const isSuspend = String(val) === STATUS_ENUM.SUSPEND;
        const isApproved = String(val) === STATUS_ENUM.APPROVE;

        if (isApproved) {
            this.setState({ isDisableClickGenerateMid: false });
        } else {
            this.setState({ isDisableClickGenerateMid: true });
        }

        if ([PROVIDER.XENDIT.id, PROVIDER.DANA.id, PROVIDER.DANA_STATIC.id].includes(String(form.idProvider))) {
            this.setState({
                isDisableGenerateMid: false,
                isDisableFieldMid: true,
            });
        } else {
            this.setState({
                isDisableGenerateMid: true,
                isDisableFieldMid: false,
            });
        }

        this.setState(
            {
                form: newForm,
                isRejected,
                isSuspend,
                isApproved,
            },
            () => {
                this.validateApprovalStatus();
            },
        );
    };

    doShowingPopupQR = (value) => {
        this.setState({
            showingPopupTestQR: value,
        });
    };

    doShowingPopupQRXendit = () => {
        const { showingPopupQRXendit } = this.state;
        this.setState({
            showingPopupQRXendit: !showingPopupQRXendit,
        });
    }

    handleShowConfirmationPopup = () => {
        const { showConfirmationPopup } = this.state;
        this.setState({
            showConfirmationPopup: !showConfirmationPopup,
        });
    }

    doCreateOrder = () => {
        this.fetchDataCreateOrder();
    };

    doCheckStatus = () => {
        this.fetchDataCheckStatus();
    };

    doUpdatedVerifiedStatus = () => {
        this.fetchDataVerifiedStatus();
    };

    fetchDataCreateOrder = async () => {
        const { showProgress, hideProgress, notificationSystem } = this.props;
        const { form } = this.state;

        showProgress();

        try {
            const payload = {
                outlet_id: Number(form.outlets[0].id),
                invoice_no: `TEST-${moment().format('HH-mm-ss-SSS')}`,
                payment_method_id: 6, // QRIS
                provider_id: Number(form.idProvider),
                bill_amount: Number(form.price),
                date: moment().format('YYYY-MM-DD HH:mm:ss'),
                name: `#cockpit-${moment().format('HH-mm-ss-SSS')}`,
                order_no: `ORDER-${moment().format('HH-mm-ss-SSS')}`,
                type: 99, // Lainnya
            };

            const response = await walletApi.createOrder(payload);

            this.setState({
                qrUrl: response.data.qr_link,
                qrCode: response.data.qr_code,
                qrImage: response.data.qr_image,
                orderNo: response.data.merchant_transaction_id,
            });

            this.doShowingPopupQR(true);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    fetchQrisStaticXendit = async () => {
        const { form } = this.state;
        const { showProgress, hideProgress, notificationSystem } = this.props;

        showProgress();

        try {
            const payload = {
                outlet_id: +form.outlets[0].id,
                provider_id: +form.idProvider,
                ...form.qr_string && { qr_string: form.qr_string },
            };
            const response = await walletApi.createQRStatic(payload);

            this.setState({
                qrStaticXendit: {
                    qrString: response.data.qr_string,
                    merchantId: response.data.merchant_id,
                    outletId: response.data.outlet_id,
                    merchantName: response.data.merchant_name,
                },
            }, () => {
                this.doShowingPopupQRXendit();
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    fetchCreateSubAccountMid = async () => {
        const { form } = this.state;
        const { showProgress, hideProgress, notificationSystem } = this.props;
        showProgress();
        const isDanaProvider = form.idProvider === PROVIDER.DANA.id || form.idProvider === PROVIDER.DANA_STATIC.id;

        try {
            const payload = {
                outlet_id: Number(form.outlets[0].id),
                email: form.companyEmail || form.ownerEmail,
                outlet_name: form.outlets[0].name,
            };
            const response = await (isDanaProvider ? walletApi.generateNmid({ provider_id: +form.idProvider, submission_id: form.merchant_biller_no, outlet_id: Number(form.outlets[0].id) }) : walletApi.createSubAccountMid(payload));
            const finalResponse = isDanaProvider ? response.data.shop_id : response.data.subaccount_id;

            if (response.data && finalResponse) {
                this.setState({
                    isDisableClickGenerateMid: false,
                });
            }

            // aku test di local mid nya gak set ke form payload nya tapi pas di prod set dengan benar
            this.setState({
                generateMidQrStatic: {
                    mid: response.data && finalResponse,
                },
            }, () => {
                notificationSystem.addNotification({
                    title: 'Sukses generate MID',
                    message: response.status.message,
                    level: 'success',
                });
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    }

    fetchDataCheckStatus = async () => {
        const { notificationSystem } = this.props;
        const { orderNo } = this.state;
        try {
            const payload = {
                biller_no: orderNo,
            };
            const response = await walletApi.getCheckStatus(payload);

            this.setState({
                paymentStatus: response.message,
            });

            if (response.code === STATUS_QR_ENUM.PAYMENT_OPEN || response.code === STATUS_QR_ENUM.PAYMENT_SUCCESS) {
                this.setState({ isVerifiedStatus: true });
            } // test QR success or verified
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        }
    };

    fetchDataVerifiedStatus = async () => {
        const {
            showProgress, hideProgress, notificationSystem, setGlobalMessage,
        } = this.props;
        const { isVerifiedStatus, form } = this.state;
        const isVerified = isVerifiedStatus ? 1 : 0;

        this.doShowingPopupQR(false);
        showProgress();

        try {
            const payload = {
                is_verified: isVerified,
                submission_no: form.merchant_biller_no,
            };
            const response = await walletApi.getVerifiedSubmission(payload);
            setGlobalMessage({
                title: response.message,
                level: 'success',
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Gagal mendapatkan data',
                message: catchError(err),
                level: 'error',
            });
        } finally {
            hideProgress();
        }
    };

    fetchDataHistory = async (state) => {
        const { page } = state;
        const { form: prevForm } = this.state;

        if (prevForm.idUser === '') {
            await this.startFetchingDetailData();
        }

        const { form } = this.state;

        const payload = {
            page: Number(page) + 1,
            parent_id: Number(form.idUser),
        };

        let retval = { data: [], pageCount: 0, err: null };
        try {
            const res = await walletApi.getMerchantList(payload);
            const { data, meta: { last_page: lastPage } } = res;

            retval = {
                ...retval,
                data,
                pageCount: Number(lastPage),
            };
        } catch (e) {
            retval = { ...retval, err: e };
        }

        return retval;
    }

    handleDuplicate = async () => {
        const {
            form: { outlets: outletsState, idProvider }, isApproved, accountingList, showConfirmationPopup,
        } = this.state;
        const {
            notificationSystem, hideProgress, setGlobalMessage, showProgress,
        } = this.props;
        const { form, providerFilterList } = this.state;

        const wallets = providerFilterList
            .filter(x => Number(x.id_bank) === Number(form.idProvider))
            .map(item => ({ ...item, mdr: form.mdr }));

        const bankData = accountingList.find(x => x.id === form.merchant_account_bank_id) || {};

        let payloadOuter = {
            status: '1',
            // tidak perlu kirim MID ketika duplicate
            // mid: isApproved && [PROVIDER.XENDIT.id, PROVIDER.DANA.id].includes(idProvider) ? this.state.generateMidQrStatic.mid || form.mid : form.mid,
            mdr: form.mdr,
            keterangan: form.statusNote,
            is_draft: 0,
            bentuk_badan_usaha: form.companyType,
            nama_badan_usaha: form.namaBadanUsaha,
            masa_berlaku_sewa_du: formatDate(
                form.masaBerlakuSewa,
                'yyyy-mm-dd',
            ),
            alamat: form.ownerAddress || form.business_info_address,
            no_ktp: form.ownerNoKtp,
            no_hp: form.ownerNoTelp,
            email: form.ownerEmail,
            nama_pemilik: form.ownerName,
            barang_jasa: form.sellProduct,
            nama_bank: form.bankName || bankData.name || '',
            no_rek_bank: form.nomorRekening || bankData.no || '',
            atas_nama_bank: form.bankAtasNama || bankData.holder || '',
            cabang_bank: form.bankBranch,
            id_bank: form.merchant_account_bank_id,
            foto_butab_bank: form.fotoButab.filename,
            email_du: form.companyEmail,
            no_hp_du: form.companyPhone,
            nama_du: form.companyOutlet,
            alamat_du: form.companyAddress,
            provinsi_du: form.companyProvince,
            negara_du: form.companyCountry,
            kota_du: form.companyCity,
            area_du: form.companySubdistrict,
            lokasi_gps_du: `${form.merchant_gps_location_latitude},${form.merchant_gps_location_longitude}`,
            kode_pos_du: form.companyPostalCode,
            status_lokasi_du: form.companyOutletStatus,
            foto_depan_du: form.fotoUsahaDepan.filename,
            foto_samping_du: form.fotoUsahaSamping.filename,
            foto_lain_du: form.fotoUsahaLain.filename,
            outlets: outletsState.map(x => x.id),
            foto_ktp: form.fotoKtp.filename,
            foto_npwp: form.fileNpwp.filename,
            berkas_form_pendaftaran: form.fileFormPendaftaranMerchant.filename,
            wallets,
            akta_pendirian: form.fileAktaPendirian.filename,
            skm: form.fileSkm.filename,
            siup: form.fileSiup.filename,
            tdp: form.fileTdp.filename,
            user_id: form.idUser,
            no_pks: form.noPks,
            is_pengajuan_wallet: 1,
            is_pengajuan_tselpoint: 0,
            is_pengajuan_grabfood: 0,
            minimum_limit: form.settlementLimit,
            business_profile: {
                business_location_business: form.businessLocation,
                business_location_business_detail:
                    form.detailBusinessLocation,
                business_link_google_maps: form.linkGoogleMaps,
                business_tax_type: form.taxType,
                business_criteria: form.criteria,
                business_establishment_place:
                    form.merchantEstablishmentPlace,
                business_establishment_date: formatDate(
                    form.merchantEstablishmentDate,
                    'yyyy-mm-dd',
                ),
                business_register_number: form.noIzinUsaha,
                business_validity_periode: form.validityPeriode,
                business_nib_number: form.nibNumber,
            },
            business_pic: {
                business_pic_name: form.picName,
                business_pic_title: form.picTitle,
                business_pic_finance_email: form.picFinanceEmail,
                business_pic_finance_phone_number:
                    form.picFinancePhoneNumber,
                business_pic_finance: form.picFinance,
            },
            business_legal: {
                legal_deed_corporation: form.fileAktaPendirian.filename,
                legal_ministerial_decree: form.fileSkm.filename,
            },
            qr_type: form.qr_type === 'dynamic' ? 'static' : 'dynamic',
            business_info: {
                address: form.business_info_address,
                id_kota: form.business_info_id_kota,
                id_provinsi: form.business_info_id_provinsi,
                id_subdistrict: form.business_info_id_subdistrict,
                kodepos: form.business_info_kodepos,
                lokasi_gps: `${form.business_info_lokasi_gps_latitude},${form.business_info_lokasi_gps_longitude}`,
            },
            identitas_pemohon: form.applicantType,
        };

        showProgress();
        try {
            payloadOuter = { ...payloadOuter, channel: 'cockpit' };

            const res = await walletApi.createMerchantBiller(payloadOuter);
            if (!res.status) throw new Error(res.msg || res);
            setGlobalMessage({
                title: 'Duplicate data Berhasil',
                level: 'success',
            });

            this.gotoBasePath();
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Duplicate data gagal',
                message: catchError(err),
                level: 'error',
            });

            this.handleShowConfirmationPopup();
        } finally {
            hideProgress();
        }
    }

    handleSaveQRISCode = async (value, callback, closeHandler) => {
        const { form } = this.state;
        const { showProgress, hideProgress, notificationSystem } = this.props;

        if (value === '') {
            notificationSystem.addNotification({
                title: 'Gagal!',
                message: 'Mohon lengkapi data QRIS',
                level: 'error',
            });
            callback();
            return;
        }

        showProgress();
        try {
            const payload = {
                qr_string: value,
                outlet_id: +form.outlets[0].id,
                provider_id: +form.idProvider,
            };
            const res = await walletApi.createQRStatic(payload);
            const { data } = res;

            const updatedForm = update(form, {
                qr_string: { $set: data.qr_string },
            });
            this.setState({
                form: updatedForm,
                isDisablePrintQRStatic: false,
            });
        } catch (error) {
            notificationSystem.addNotification({
                title: 'Gagal!',
                message: catchError(error),
                level: 'error',
            });
        } finally {
            callback();
            closeHandler();
            hideProgress();
        }
    }

    render() {
        const { params } = this.props;
        const {
            form,
            companyTypeList,
            provinceList,
            countryList,
            cityList,
            cityListOutlet,
            districtList,
            districtListOutlet,
            outletStatusList,
            approvalStatusList,
            // walletPaymentData,
            isApproved,
            isEditDisabled,
            innerWidthScreen,
            footerButtons,
            branchList,
            userList,
            showingWalletPaymentPopup,
            showingPopupTestQR,
            providerFilterList,
            walletProvider,
            akuntingList,
            idAkunting,
            isSuspend,
            isRejected,
            qrUrl,
            qrCode,
            qrImage,
            isDisabledTestQR,
            isVerifiedStatus,
            paymentStatus,
            merchantDataFound,
            showingPopupQRXendit,
            qrStaticXendit,
            isDisablePrintQRStatic,
            generateMidQrStatic,
            isDisableGenerateMid,
            isDisableFieldMid,
            isDisableClickGenerateMid,
            accountingList,
            showConfirmationPopup,
            isShowModalUploadQRISCode,
        } = this.state;

        const isPerseorangan = String(form.companyType) === COMPANY_ENUM.PERSEORANGAN;
        const fixedFooterWidth = innerWidthScreen - 265;
        const isCreate = params.type === FORM_TYPES.CREATE;

        return (
            <div>
                {
                    !merchantDataFound
                        ? (<DataNotFound />)
                        : (
                            <FormValidation
                                ref={(c) => {
                                    this.form = c;
                                }}
                            >
                                <div className="row">
                                    <div className="col-sm-12">
                                        <div className="panel">
                                            <div className="panel-heading">
                                                <h4 className="title">Status</h4>
                                            </div>
                                            <div className="panel-body">
                                                <div className="row mb-sm">
                                                    <div className="col-sm-4">
                                                        <div>
                                                            <label className="control-label">
                                                                User
                                                                {' '}
                                                                <span className="text-red">
                                                                    *
                                                                </span>
                                                            </label>
                                                            {params.type === FORM_TYPES.VIEW
                                                                ? (
                                                                    <InputText
                                                                        changeEvent={() => {
                                                                            // do noting
                                                                        }}
                                                                        value={
                                                                            form.userNameEmaill
                                                                        }
                                                                        disabled={
                                                                            isEditDisabled
                                                                        }
                                                                        name="id_user_hidden"
                                                                    />
                                                                ) : (
                                                                    <div className="form-group">
                                                                        <InputUser
                                                                            userList={userList}
                                                                            value={form.idUser}
                                                                            onFetch={
                                                                                this
                                                                                    ._onFetchUser
                                                                            }
                                                                            changeEvent={
                                                                                this
                                                                                    .changeUserHandler
                                                                            }
                                                                            disabled={
                                                                                isEditDisabled
                                                                            }
                                                                        />
                                                                        <div className="form-group hidden">
                                                                            <input
                                                                                ref={(c) => {
                                                                                    this.hiddenIdUser = c;
                                                                                }}
                                                                                name="id_user_hidden"
                                                                                type="text"
                                                                                value={
                                                                                    form.idUser
                                                                                }
                                                                                onChange={() => {
                                                                                    // do noting
                                                                                }}
                                                                                required
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            <FieldFeedbacks for="id_user_hidden">
                                                                <FieldFeedback when="valueMissing">
                                                                    User wajib diisi
                                                                </FieldFeedback>
                                                            </FieldFeedbacks>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div>
                                                            <label className="control-label">
                                                                Pilih Status
                                                                {' '}
                                                                <span className="text-red">
                                                                    *
                                                                </span>
                                                            </label>
                                                            <Select
                                                                changeEvent={this.changeApprovalStatusHandler}
                                                                value={form.approvalStatus}
                                                                data={approvalStatusList}
                                                                disabled={!isEditDisabled}
                                                            />
                                                            <div className="form-group hidden">
                                                                <input
                                                                    ref={(c) => {
                                                                        this.hiddenApprovalStatus = c;
                                                                    }}
                                                                    name="approval_status_hidden"
                                                                    type="text"
                                                                    value={
                                                                        form.approvalStatus
                                                                    }
                                                                    onChange={() => {
                                                                        // do noting
                                                                    }}
                                                                    required
                                                                />
                                                            </div>
                                                            <FieldFeedbacks for="approval_status_hidden">
                                                                <FieldFeedback
                                                                    when={value => String(value)
                                                                        === '0'
                                                                    }
                                                                >
                                                                    Status wajib diisi
                                                                </FieldFeedback>
                                                            </FieldFeedbacks>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <label className="control-label">
                                                            No. PKS
                                                            {' '}
                                                            {isEditDisabled && (
                                                                <span className="text-red">
                                                                    *
                                                                </span>
                                                            )}
                                                        </label>
                                                        <InputText
                                                            changeEvent={(val, e) => this._changeInputHandler('noPks', val, e)}
                                                            value={form.noPks}
                                                            name="pks_number"
                                                            disabled={!isEditDisabled}
                                                        />
                                                        {isEditDisabled && (
                                                            <FieldFeedbacks for="pks_number">
                                                                <FieldFeedback
                                                                    when={value => String(value).length
                                                                        === 0
                                                                        || parseFloat(value) <= '0'
                                                                    }
                                                                >
                                                                    Nomor PKS wajib diisi
                                                                </FieldFeedback>
                                                            </FieldFeedbacks>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="row mb-sm">
                                                    <div className="col-sm-4">
                                                        <Select
                                                            label="Provider"
                                                            changeEvent={(val, e) => this._changeInputHandler('idProvider', val, e)}
                                                            value={form.idProvider}
                                                            data={providerFilterList}
                                                            disabled={!isEditDisabled}
                                                        />
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <label className="control-label">
                                                            MID
                                                            {' '}
                                                            {isApproved && (
                                                                <span className="text-red">
                                                                    *
                                                                </span>
                                                            )}
                                                        </label>
                                                        <InputText
                                                            changeEvent={(val, e) => this._changeInputHandler('mid', val, e)}
                                                            value={generateMidQrStatic.mid ? generateMidQrStatic.mid : form.mid}
                                                            name="mid_value"
                                                            required={isApproved}
                                                            disabled={isDisableFieldMid || !isEditDisabled}
                                                        />
                                                        <FieldFeedbacks for="mid_value">
                                                            <FieldFeedback when="valueMissing">
                                                                MID wajib diisi
                                                            </FieldFeedback>
                                                        </FieldFeedbacks>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <label className="control-label">
                                                            MDR + Biaya Layanan
                                                            {' '}
                                                            {isApproved && (
                                                                <span className="text-red">
                                                                    *
                                                                </span>
                                                            )}
                                                        </label>
                                                        <InputPercent
                                                            value={form.mdr}
                                                            changeEvent={(val, e) => this._changeInputHandler('mdr', val, e)}
                                                            name="mdr_number"
                                                            desimal
                                                            maxDecimalPlaceLen={2}
                                                            disabled={!isEditDisabled}
                                                        />
                                                        <FieldFeedbacks for="mdr_number">
                                                            <FieldFeedback
                                                                when={value => String(value).length === 0 || parseFloat(value) <= '0' && isApproved}
                                                            >
                                                                MDR + Biaya Layanan
                                                                wajib diisi
                                                            </FieldFeedback>
                                                        </FieldFeedbacks>
                                                    </div>
                                                </div>
                                                <div className="row mb-sm">
                                                    <div className="col-sm-12">
                                                        <label className="control-label">
                                                            Keterangan
                                                            {' '}
                                                            {(isRejected || isSuspend) && (
                                                                <span className="text-red">
                                                                    *
                                                                </span>
                                                            )}
                                                        </label>
                                                        <TextArea
                                                            row={2}
                                                            changeEvent={(val, e) => this._changeInputHandler('statusNote', val, e)}
                                                            value={form.statusNote}
                                                            name="status_note"
                                                            disabled={!isEditDisabled}
                                                        />
                                                        <div className="form-group hidden">
                                                            <input
                                                                ref={(c) => {
                                                                    this.hiddenStatusNote = c;
                                                                }}
                                                                name="status_note_hidden"
                                                                type="text"
                                                                value={form.statusNote}
                                                                required={isRejected || isSuspend}
                                                            />
                                                        </div>
                                                        <FieldFeedbacks for="status_note_hidden">
                                                            <FieldFeedback when="valueMissing">
                                                                Keterangan wajib diisi
                                                            </FieldFeedback>
                                                        </FieldFeedbacks>
                                                    </div>
                                                </div>

                                                {params.type === FORM_TYPES.VIEW ? (
                                                    <div className="row mb-sm">
                                                        <div className="col-sm-8" style={{ display: 'flex' }}>
                                                            <div style={{ width: 200, marginRight: 30 }}>
                                                                <label className="control-label">
                                                                    Price
                                                                    {String(form.approvalStatus) === STATUS_ENUM.APPROVE && PROVIDER.OVO.id === form.idProvider && ' - OVO minimum transaction 1000'}
                                                                    {isVerifiedStatus && (
                                                                        <img
                                                                            className="ml-xs"
                                                                            src={
                                                                                verifiedStatusIcon
                                                                            }
                                                                            alt=""
                                                                        />
                                                                    )}
                                                                </label>
                                                                <InputCurrency
                                                                    id="price"
                                                                    name="price"
                                                                    value={form.price}
                                                                    changeEvent={(val, e) => this._changeInputHandler('price', val, e)}
                                                                />
                                                            </div>
                                                            <div
                                                                className="mt-xlg"
                                                                style={{ marginRight: 15 }}
                                                            >
                                                                <button
                                                                    type="button"
                                                                    className="btn btn-primary"
                                                                    disabled={isDisabledTestQR}
                                                                    onClick={this.doCreateOrder}
                                                                >
                                                                    Test QR
                                                                </button>
                                                            </div>
                                                            {!isDisablePrintQRStatic && (
                                                                <div
                                                                    className="mt-xlg"
                                                                    style={{ marginRight: 15 }}
                                                                >
                                                                    <button
                                                                        type="button"
                                                                        className="btn btn-secondary"
                                                                        onClick={this.fetchQrisStaticXendit}
                                                                    >
                                                                        Print QRIS Code
                                                                    </button>
                                                                </div>
                                                            )}
                                                            {(PROVIDER.DANA_STATIC.id === form.idProvider
                                                                && isApproved
                                                                && (generateMidQrStatic.mid ? generateMidQrStatic.mid : form.mid))
                                                                && (
                                                                <React.Fragment>
                                                                    <div
                                                                        className="mt-xlg"
                                                                        style={{ marginRight: 15 }}
                                                                    >
                                                                        <button
                                                                            type="button"
                                                                            className="btn btn-primary"
                                                                            onClick={() => this.setState({ isShowModalUploadQRISCode: true })}
                                                                        >
                                                                            Upload QRIS
                                                                        </button>
                                                                    </div>
                                                                </React.Fragment>
                                                            )}
                                                            <div
                                                                className="mt-xlg"
                                                            >
                                                                <button
                                                                    type="button"
                                                                    className="btn btn-secondary"
                                                                    style={{
                                                                        display: isDisableGenerateMid ? 'none' : 'block',
                                                                    }}
                                                                    disabled={isDisableClickGenerateMid}
                                                                    onClick={this.fetchCreateSubAccountMid}
                                                                >
                                                                    Generate MID
                                                                </button>
                                                            </div>
                                                        </div>
                                                        <div className="col-sm-2">
                                                            <label className="control-label">
                                                                QRIS Type
                                                            </label>
                                                            <InputText
                                                                value={form.qr_type === 'static' ? 'QR Static' : 'QR Dynamic'}
                                                                disabled
                                                            />
                                                        </div>
                                                        <div className="col-sm-2">
                                                            <label className="control-label">
                                                                Channel
                                                            </label>
                                                            <InputText
                                                                value={form.channel}
                                                                disabled
                                                            />
                                                        </div>
                                                    </div>
                                                ) : null}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col-sm-6">
                                        {String(form.idUser).length > 0 && (
                                            <div className="panel">
                                                <div className="panel-heading">
                                                    <h4 className="title">Data Outlet</h4>
                                                </div>
                                                <div className="panel-body">
                                                    <MultiTagWithPopup
                                                        changeEvent={
                                                            this.changeOutletHandler
                                                        }
                                                        btnText="Pilih Outlet"
                                                        data={branchList}
                                                        value={form.outlets}
                                                        titlePlaceholder="Outlet"
                                                        description="Pilih Outlet"
                                                        showCheckAll
                                                        disabled={isEditDisabled}
                                                    />
                                                    <div className="form-group hidden">
                                                        <input
                                                            ref={(c) => {
                                                                this.hiddenOutlet = c;
                                                            }}
                                                            name="id_outlet_hidden"
                                                            type="text"
                                                            value={
                                                                form.outlets
                                                            }
                                                            required
                                                        />
                                                    </div>
                                                    <FieldFeedbacks for="id_outlet_hidden">
                                                        <FieldFeedback when="valueMissing">
                                                            Outlet Wajib Disini
                                                        </FieldFeedback>
                                                    </FieldFeedbacks>
                                                </div>
                                            </div>
                                        )}

                                        <DataOwner
                                            form={form}
                                            companyTypeList={companyTypeList}
                                            isEditDisabled={isEditDisabled}
                                            onChange={this._changeInputHandler}
                                            onChangeNumber={this.changeNumberValueHandler}
                                            ciraterialMerchant={ciraterialMerchant}
                                        />

                                        {isPerseorangan && (
                                            <Fragment>
                                                <DataAddressOwner
                                                    provinceList={provinceList}
                                                    districtList={districtList}
                                                    cityList={cityList}
                                                    form={form}
                                                    isEditDisabled={isEditDisabled}
                                                    onChange={this._changeInputHandler}
                                                />
                                            </Fragment>
                                        )}

                                        {!isPerseorangan && (
                                            <DataAdmin
                                                form={form}
                                                isEditDisabled={isEditDisabled}
                                                onChange={this._changeInputHandler}
                                            />
                                        )}
                                    </div>
                                    <div className="col-sm-6">
                                        {!isPerseorangan && (
                                            <DataAddressOwner
                                                provinceList={provinceList}
                                                districtList={districtList}
                                                cityList={cityList}
                                                form={form}
                                                isEditDisabled={isEditDisabled}
                                                onChange={this._changeInputHandler}
                                            />
                                        )}

                                        <DataBusinessAccount
                                            form={form}
                                            isEditDisabled={isEditDisabled}
                                            onChange={this._changeInputHandler}
                                            onChangeNumber={this.changeNumberValueHandler}
                                            notifyUploadFailed={this.notifyUploadFailed}
                                            fileUploadService={fileUploadService}
                                            settlementLimitList={settlementLimitList}
                                            accountingList={accountingList}
                                        />

                                        <div className="form-group hidden">
                                            <input
                                                ref={c => this.companyProvinceHidden = c}
                                                name="companyProvinceHidden"
                                                type="text"
                                                value={form.companyProvince}
                                                onChange={() => { }}
                                                required
                                            />
                                            <input
                                                ref={c => this.companyCityHidden = c}
                                                name="companyCityHidden"
                                                type="text"
                                                value={form.companyCity}
                                                onChange={() => { }}
                                                required
                                            />
                                            <input
                                                ref={c => this.companySubdistrictHidden = c}
                                                name="companySubdistrictHidden"
                                                type="text"
                                                value={form.companySubdistrict}
                                                onChange={() => { }}
                                                required
                                            />
                                            <input
                                                ref={c => this.business_info_id_provinsi_hidden = c}
                                                name="business_info_id_provinsi_hidden"
                                                type="text"
                                                value={form.business_info_id_provinsi}
                                                onChange={() => { }}
                                                required
                                            />
                                            <input
                                                ref={c => this.business_info_id_kota_hidden = c}
                                                name="business_info_id_kota_hidden"
                                                type="text"
                                                value={form.business_info_id_kota}
                                                onChange={() => { }}
                                                required
                                            />
                                            <input
                                                ref={c => this.business_info_id_subdistrict_hidden = c}
                                                name="business_info_id_subdistrict_hidden"
                                                type="text"
                                                value={form.business_info_id_subdistrict}
                                                onChange={() => { }}
                                                required
                                            />
                                        </div>

                                        <DataOutlet
                                            provinceList={provinceList}
                                            cityList={cityListOutlet}
                                            districtList={districtListOutlet}
                                            form={form}
                                            isEditDisabled={isEditDisabled}
                                            onChange={this._changeInputHandler}
                                        />
                                    </div>
                                </div>
                                <div>
                                    {/* <div className="row hidden"> note: if you want to use this in cockpit v2, change to component table v2
                                    <div className="col-sm-12">
                                        <div className="panel">
                                            <div className="panel-heading">
                                                <h4 className="title">
                                                    {isEditDisabled ? 'Jenis Pembayaran Wallet' : 'Pilih Jenis Pembayaran Wallet'}
                                                </h4>
                                            </div>
                                            <div className="panel-body">
                                                {isEditDisabled && (
                                                    <div>
                                                        <Table
                                                            content={walletPaymentData}
                                                            externalMeta={tableMetaDetail(this.showWalletPaymentPopupHandler)}
                                                            columns={['provider', 'mbr', 'accountingName']}
                                                            classes="interactive-table"
                                                            rowEvent={() => {
                                                                // do noting
                                                            }}
                                                            externalPageLimit={walletPaymentData.length}
                                                            externalFilterKeyword=""
                                                            disabledPagination
                                                        />
                                                    </div>
                                                )}
                                                {!isEditDisabled && (
                                                    <div>
                                                        <Table
                                                            content={walletPaymentData}
                                                            externalMeta={tableMetaDetail(this.showWalletPaymentPopupHandler)}
                                                            columns={[
                                                                'provider', 'mbr', 'accountingName', 'edit']}
                                                            classes="interactive-table"
                                                            rowEvent={() => {
                                                                // do nothing
                                                            }}
                                                            externalPageLimit={walletPaymentData.length}
                                                            externalFilterKeyword=""
                                                            disabledPagination
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div> */}

                                    <div className="row">
                                        <div className="col-sm-12">
                                            <div className="panel">
                                                <div className="panel-heading">
                                                    <h4 className="title">Data Lampiran</h4>
                                                    <h5
                                                        className="subtitle"
                                                        style={{ fontSize: '12px' }}
                                                    >
                                                        ( maximum file size : 5MB; format:
                                                        .png, .jpg, .jpeg, .pdf, .doc, .docx
                                                        )
                                                    </h5>
                                                </div>
                                                <div className="panel-body">
                                                    {!isPerseorangan ? (
                                                        <Fragment>
                                                            <div className="row">
                                                                <div className="col-sm-6">
                                                                    <div className="mb-sm">
                                                                        <FileUpload
                                                                            label="KITAS / KTP"
                                                                            value={form.fotoKtp}
                                                                            idUser={form.idUser}
                                                                            acceptUpload=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*"
                                                                            name="foto_ktp"
                                                                            required
                                                                            changeEvent={(val) => { this._changeInputHandler('fotoKtp', val); }}
                                                                            onError={this.notifyUploadFailed}
                                                                            disabled={isEditDisabled}
                                                                            mainURL={fileUploadService}
                                                                        />
                                                                        <div className="form-group hidden">
                                                                            <input
                                                                                ref={c => this.refFotoKtp = c}
                                                                                name="foto_ktp_hidden"
                                                                                type="text"
                                                                                value={form.fotoKtp.path}
                                                                                onChange={() => { }}
                                                                                required
                                                                            />
                                                                        </div>
                                                                        {isCreate && (
                                                                            <FieldFeedbacks for="foto_ktp_hidden">
                                                                                <FieldFeedback when="valueMissing">
                                                                                    {errorMessages.ktp}
                                                                                </FieldFeedback>
                                                                            </FieldFeedbacks>
                                                                        )}
                                                                    </div>
                                                                    {/* <div className="mb-sm">
                                                                        <FileUpload
                                                                            label="NPWP"
                                                                            value={form.fileNpwp}
                                                                            idUser={form.idUser}
                                                                            acceptUpload=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*"
                                                                            name="npwp"
                                                                            required
                                                                            changeEvent={(val) => { this._changeInputHandler('fileNpwp', val); }}
                                                                            onError={this.notifyUploadFailed}
                                                                            disabled={isEditDisabled}
                                                                            mainURL={fileUploadService}
                                                                        />
                                                                    </div> */}
                                                                    <div className="mb-sm">
                                                                        <FileUpload
                                                                            label="Akta Pendirian"
                                                                            value={form.fileAktaPendirian}
                                                                            idUser={form.idUser}
                                                                            name="akta_pendirian"
                                                                            acceptUpload=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*"
                                                                            required
                                                                            changeEvent={(val) => { this._changeInputHandler('fileAktaPendirian', val); }}
                                                                            onError={this.notifyUploadFailed}
                                                                            disabled={isEditDisabled}
                                                                            mainURL={fileUploadService}
                                                                        />
                                                                        <div className="form-group" />
                                                                        <FieldFeedbacks for="file_akta_pendirian_hidden">
                                                                            <FieldFeedback when="valueMissing">
                                                                                {errorMessages.akta}
                                                                            </FieldFeedback>
                                                                        </FieldFeedbacks>
                                                                    </div>
                                                                </div>
                                                                <div className="col-sm-6">
                                                                    <div className="mb-sm">
                                                                        <FileUpload
                                                                            label="Surat Keputusan Menteri"
                                                                            value={form.fileSkm}
                                                                            idUser={form.idUser}
                                                                            name="skm"
                                                                            acceptUpload=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*"
                                                                            required
                                                                            changeEvent={(val) => { this._changeInputHandler('fileSkm', val); }}
                                                                            onError={this.notifyUploadFailed}
                                                                            disabled={isEditDisabled}
                                                                            mainURL={fileUploadService}
                                                                        />
                                                                        <div className="form-group" />
                                                                        <FieldFeedbacks for="file_skm_hidden">
                                                                            <FieldFeedback when="valueMissing">
                                                                                {errorMessages.skm}
                                                                            </FieldFeedback>
                                                                        </FieldFeedbacks>
                                                                    </div>
                                                                    <div className="mb-sm">
                                                                        <FileUpload
                                                                            label="Surat Izin Usaha Perdagangan (SIUP)"
                                                                            value={form.fileSiup}
                                                                            idUser={form.idUser}
                                                                            name="siup"
                                                                            acceptUpload=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*"
                                                                            required
                                                                            changeEvent={(val) => { this._changeInputHandler('fileSiup', val); }}
                                                                            onError={this.notifyUploadFailed}
                                                                            disabled={isEditDisabled}
                                                                            mainURL={fileUploadService}
                                                                        />
                                                                        <div className="form-group" />
                                                                        <FieldFeedbacks for="file_siup_hidden">
                                                                            <FieldFeedback when="valueMissing">
                                                                                {errorMessages.tdp}
                                                                            </FieldFeedback>
                                                                        </FieldFeedbacks>
                                                                    </div>
                                                                    <div className="mb-sm">
                                                                        <FileUpload
                                                                            label="Nomor Izin Usaha (NIB)"
                                                                            value={form.fileTdp}
                                                                            idUser={form.idUser}
                                                                            name="tdp"
                                                                            acceptUpload=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*"
                                                                            required
                                                                            changeEvent={(val) => { this._changeInputHandler('fileTdp', val); }}
                                                                            onError={this.notifyUploadFailed}
                                                                            disabled={isEditDisabled}
                                                                            mainURL={fileUploadService}
                                                                        />
                                                                        <div className="form-group" />
                                                                        <FieldFeedbacks for="file_tdp_hidden">
                                                                            <FieldFeedback when="valueMissing">
                                                                                {errorMessages.tdp}
                                                                            </FieldFeedback>
                                                                        </FieldFeedbacks>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </Fragment>
                                                    ) : (
                                                        <Fragment>
                                                            <div className="row">
                                                                <div className="col-sm-6">
                                                                    <div className="mb-sm">
                                                                        <FileUpload
                                                                            label="KITAS / KTP"
                                                                            value={form.fotoKtp}
                                                                            idUser={form.idUser}
                                                                            acceptUpload=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*"
                                                                            name="foto_ktp"
                                                                            required
                                                                            changeEvent={(val) => { this._changeInputHandler('fotoKtp', val); }}
                                                                            onError={this.notifyUploadFailed}
                                                                            disabled={isEditDisabled}
                                                                            mainURL={fileUploadService}
                                                                        />
                                                                        <div className="form-group hidden">
                                                                            <input
                                                                                ref={c => this.refFotoKtp = c}
                                                                                name="foto_ktp_hidden"
                                                                                type="text"
                                                                                value={form.fotoKtp.path}
                                                                                onChange={() => { }}
                                                                                required
                                                                            />
                                                                        </div>
                                                                        {isCreate && (
                                                                            <FieldFeedbacks for="foto_ktp_hidden">
                                                                                <FieldFeedback when="valueMissing">
                                                                                    {errorMessages.ktp}
                                                                                </FieldFeedback>
                                                                            </FieldFeedbacks>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                                {/* <div className="col-sm-6">
                                                                    <div className="mb-sm">
                                                                        <FileUpload
                                                                            label="NPWP"
                                                                            value={form.fileNpwp}
                                                                            idUser={form.idUser}
                                                                            acceptUpload=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf,image/*"
                                                                            name="npwp"
                                                                            required={false}
                                                                            changeEvent={(val) => { this._changeInputHandler('fileNpwp', val); }}
                                                                            onError={this.notifyUploadFailed}
                                                                            disabled={isEditDisabled}
                                                                            mainURL={fileUploadService}
                                                                        />
                                                                    </div>
                                                                </div> */}
                                                            </div>
                                                        </Fragment>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {params.type === FORM_TYPES.VIEW ? (
                                        <TableHistory onFetch={this.fetchDataHistory} />
                                    ) : null}
                                </div>
                                <br />
                            </FormValidation>
                        )
                }
                {showingWalletPaymentPopup && (
                    <WalletPaymentPopup
                        provider={walletProvider}
                        confirmHandle={this.saveWalletPaymentHandler}
                        akuntingList={akuntingList}
                        idAkunting={idAkunting}
                        onHide={this.hideWalletPaymentPopup}
                    />
                )}

                {showingPopupTestQR && (
                    <PopupTestingQR
                        onHide={this.doUpdatedVerifiedStatus}
                        checkedStatus={this.doCheckStatus}
                        outlet={form.outlets[0].name}
                        mid={form.mid}
                        price={form.price}
                        imageURL={qrUrl}
                        qrCode={qrCode}
                        qrImage={qrImage}
                        status={paymentStatus}
                        isVerifiedStatus={isVerifiedStatus}
                    />
                )}

                <PopupPrintQRXendit
                    show={showingPopupQRXendit}
                    onHide={this.doShowingPopupQRXendit}
                    qrData={qrStaticXendit}
                />

                <DuplicateConfirmation
                    show={showConfirmationPopup}
                    onHide={this.handleShowConfirmationPopup}
                    onSubmit={this.handleDuplicate}
                    qrType={form.qr_type}
                />

                <ModalUploadQRISCode
                    isShow={isShowModalUploadQRISCode}
                    closeModal={() => this.setState({ isShowModalUploadQRISCode: false })}
                    QRISValue={form.qr_string}
                    onSubmit={this.handleSaveQRISCode}
                />

                <div
                    className="fixed-button-wrapper-bottom"
                    style={{ width: fixedFooterWidth, zIndex: 1009 }}
                >
                    <div className="row mb-lg">
                        <div className='col-sm-6'>
                            {form.approvalStatus === '4' && form.canDuplicate !== 'cant be duplicated' && (
                                <button
                                    type="button"
                                    className="btn"
                                    onClick={this.handleShowConfirmationPopup}
                                >
                                    Duplicate
                                </button>
                            )}
                        </div>
                        <div className="col-sm-6 text-right">
                            {footerButtons.map(x => (
                                <button
                                    key={x.id}
                                    type="button"
                                    className={
                                        x.type ? `btn btn-${x.type}` : 'btn'
                                    }
                                    onClick={
                                        !x.isDisabled
                                            ? x.action
                                            : () => {
                                                // do noting
                                            }
                                    }
                                    disabled={x.isDisabled}
                                >
                                    {x.content}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

export default CoreHOC(AddWallet);

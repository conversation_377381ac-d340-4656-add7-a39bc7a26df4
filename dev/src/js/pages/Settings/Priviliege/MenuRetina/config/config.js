import React from 'react';
import { ReactTableDefaults } from 'react-table';

import Expander from '../../components/CellExpander';
import IndentLevel from '../../components/CellIndentLevel';
import IsDisplay from '../../components/CellIsDisplay';
import ControllerFunction from '../../components/CellControllerFunction';
import Detail from '../../components/CellDetail';
import DateTimeColumn from '../../../../../components/table/components/DateTimeColumn';

const buildColumns = openFormEventHandler => ([
    {
        ...ReactTableDefaults,
        width: 100,
        id: 'expander',
        expander: true,
        Expander,
    },
    {
        width: 200,
        Header: 'Indonesia Name',
        accessor: 'name',
        Cell: IndentLevel,
    },
    {
        width: 200,
        Header: 'English Name',
        accessor: 'english_name',
    },
    {
        Header: 'Parent',
        accessor: 'parent_name',
    },
    {
        Header: 'Seq',
        accessor: 'urutan',
    },
    {
        Header: 'Is Display',
        accessor: 'menu_is_display',
        Cell: IsDisplay,
    },
    {
        Header: 'Controller/Function',
        accessor: 'menu_crontroller',
        Cell: ControllerFunction,
    },
    {
        Header: 'Content',
        accessor: 'content',
    },
    {
        Header: 'Updated By',
        accessor: 'updateby',
    },
    {
        Header: 'Last Update',
        accessor: 'updatedate',
        Cell: DateTimeColumn,
    },
    {
        Header: 'Detail',
        Cell: props => <Detail {...props} clickHandler={openFormEventHandler} />,
    },
]);

const FILTER_OPTIONS = [
    'All Data',
    'Display Menu',
    'Not Display As Menu',
];

const SETTING_TYPES = [
    {
        id: '1',
        value: 'Dasboard Menu',
    },
    {
        id: '2',
        value: 'Group',
    },
    {
        id: '3',
        value: 'Endpoint Only',
    },
    {
        id: '4',
        value: 'Endpoint White List',
    },
    {
        id: '5',
        value: 'Endpoint White List (Android Only)',
    },
    {
        id: '6',
        value: 'Component',
    },
];

const METHOD_ACTION = {
    view: 'View',
    create: 'Create',
    update: 'Update',
    delete: 'Delete',
    void: 'Void',
};

const ACTIVE_STATUS = {
    STRING: 'Active',
    NUMBER: '1',
};

export {
    buildColumns, FILTER_OPTIONS, SETTING_TYPES, METHOD_ACTION, ACTIVE_STATUS,
};

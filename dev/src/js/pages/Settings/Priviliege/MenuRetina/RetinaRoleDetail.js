import React, { Component } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';

import CoreHOC from '../../../../core/CoreHOC';

import InputText from '../../../../components/form/InputText';
import Switch from '../../../../components/form/Switch';
import Button from '../../../../components/form/Button';
import Select from '../../../../components/form/Select';
import Textarea from '../../../../components/form/Textarea';
import AutoComplete from '../../../../components/form/Autocomplete';

import {
  getAllCmsRoleMenu, updateCmsRole, getRoleName, UpdateRoleName,
} from '../../../../data/setting/privilage';
import { setCmsMenuOptions } from '../../../../data/setting/privilage/selectors';


class RetinaRoleDetail extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.node,
    params: PropTypes.shape({
      id: PropTypes.string,
    }),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    notificationSystem: null,
    params: {
      id: '',
    },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
  }

  constructor(props) {
    super(props);

    this.state = {
      data: [],
      expandedRows: [],
      jarak: [],
      clicked: [],
      tempExpand: [],
      filterKeyword: '',
      allData: [],
      accessable: [],
      dataTemp: [],
      /* form */
      name: '',
      landing: '',
      desc: '',
      isCanLoginAndroid: 0,
      /* end form */
      roleMenuOpt: [],
      fix: [],
      jenisTerpilih: 'All Data',
    };
  }

  componentWillMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([]);
    this.getData();
  }

  getData() {
    const { params, notificationSystem } = this.props;
    const param = {
      id: params.id,
      is_cms: 1,
    };
    getAllCmsRoleMenu(param).then((response) => {
      const accessable = [];
      response.data.forEach((dat) => {
        if (dat.id_m_cms_privilege != null) {
          accessable.push(dat.id);
        }
      });
      this.setState({
        data: response.data,
        allData: response.data,
        roleMenuOpt: setCmsMenuOptions(response.data),
        accessable,
      });
    }).catch(() => {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: '',
        level: 'error',
      });
    });

    getRoleName(param).then((response) => {
      if (response.status && response.data) {
        const { data } = response;
        this.setState({
          name: 'role' in data && data.role ? data.role : '',
          landing: 'landing' in data && data.landing ? data.landing : '',
          desc: 'desc' in data && data.desc ? data.desc : '',
          isCanLoginAndroid: 'is_can_login_android' in data && data.is_can_login_android ? parseInt(data.is_can_login_android, 10) : 0,
        });
      }
    }).catch(() => {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: '',
        level: 'error',
      });
    });
  }

  // FIXME: double reload when getData
  updateAll = async () => {
    const { params, notificationSystem } = this.props;
    const {
      name, fix, desc, landing, isCanLoginAndroid,
    } = this.state;
    const param = {
      id: params.id,
      is_cms: 1,
      name,
      fix,
      desc,
      landing,
      is_can_login_android: isCanLoginAndroid,
    };

    try {
      await updateCmsRole(param);
      this.getData();
      this.setState({
        dataTemp: [],
      });

      const res = await UpdateRoleName(param);
      this.setState({
        name: res.data,
      }, () => {
        this.getData();
      });

      notificationSystem.addNotification({
        title: 'Berhasil',
        message: 'Update success',
        level: 'success',
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Update failed',
        message: '',
        level: 'error',
      });
    }
  }

  removeChild = (id) => {
    const { allData } = this.state;
    const data = allData.filter(x => x.parent_id === id);
    const { dataTemp } = this.state;
    data.forEach((dat) => {
      dataTemp.push(dat.id);
      this.setState({
        dataTemp,
      }, () => {
        this.removeChild(dat.id);
      });
    });
  }

  updateHakAkses= (dataLama, jarak) => {
    const { id } = dataLama;
    let { accessable } = this.state;
    const { allData } = this.state;
    const dataTemp = [];
    if (accessable.indexOf(id) === -1) {
      accessable.push(id);
      allData.forEach((dat) => {
        if (dat.id === id) {
          dat.id_m_cms_privilege = id;
          dataTemp.push(dat);
        } else {
          dataTemp.push(dat);
        }
      });
    } else {
      accessable = accessable.filter(x => x !== id);
      allData.forEach((dat) => {
        if (dat.id === id) {
          dat.id_m_cms_privilege = null;
          dataTemp.push(dat);
        } else {
          dataTemp.push(dat);
        }
      });
      this.removeChild(id);
    }

    this.setState({
      data: dataTemp,
      accessable,
    }, () => {
      this.handleRowClick(dataLama, jarak);
      const fix = accessable.filter(function (n) { return !this.has(n); }, new Set(this.state.dataTemp));
      const { data } = this.state;
      const newData = [];
      data.forEach((dat) => {
        if (fix.indexOf(dat.id) === -1) {
          dat.id_user_access = null;
          newData.push(dat);
        } else {
          dat.id_user_access = 'Yes';
          newData.push(dat);
        }
      });

      this.setState({
        fix,
        data: newData,
      });
    });
  }

  cleanExpand = (id) => {
    const { tempExpand, data } = this.state;
    let dataArray = tempExpand;
    const cari = data.filter(x => x.parent_id === id);

    if (cari.length > 0) {
      cari.forEach((dat) => {
        dataArray = dataArray.filter(d => d !== id);
        const cariLagi = data.filter(x => x.parent_id === dat.id);
        if (cariLagi.length > 0) {
          this.setState({
            tempExpand: dataArray,
            expandedRows: dataArray,
          }, () => {
            this.cleanExpand(dat.id);
          });
        } else {
          this.setState({ expandedRows: dataArray, clicked: dataArray });
        }
      });
    } else {
      this.setState({ expandedRows: dataArray });
    }
  }

  handleRowClick = (row, range) => {
    const rowId = row.id;
    const { jarak, expandedRows: currentExpandedRows } = this.state;
    let { clicked } = this.state;
    jarak[rowId] = range + 1;

    const isRowCurrentlyExpanded = currentExpandedRows.includes(rowId);
    const newExpandedRows = isRowCurrentlyExpanded
      ? currentExpandedRows.filter(id => id !== rowId)
      : currentExpandedRows.concat(rowId);
    clicked = isRowCurrentlyExpanded
      ? clicked.filter(c => c !== rowId) : clicked.concat(rowId);
    if (isRowCurrentlyExpanded) {
      this.setState({
        tempExpand: newExpandedRows,
        jarak,
        clicked,
      }, () => {
        this.cleanExpand(rowId);
      });
    } else {
      this.setState({ expandedRows: newExpandedRows, jarak, clicked });
    }
  }

  searchData = (value) => {
    const { allData } = this.state;
    let filteredData = value === '' ? allData : [];
    if (allData.length > 0) {
      const val = value.toLowerCase();
      filteredData = allData.filter(p => p.name.toLowerCase().includes(val));
    }

    this.setState({
      data: filteredData,
      filterKeyword: value,
    });
  }

  changeCustomJenis = (value) => {
    const { allData } = this.state;
    let filteredData = [];
    if (value === 'All Data') {
      filteredData = allData;
    } else if (value === 'Display Menu') {
      filteredData = allData.filter(p => p.menu_is_display === '1');
    } else {
      filteredData = allData.filter(p => p.menu_is_display === '0');
    }

    this.setState({
      data: filteredData,
      jenisTerpilih: value,
    });
  }

  descChangeHandler = (val) => {
    this.setState({ desc: val });
  }

  landingChangeHandler = (val) => {
    this.setState({ landing: val });
  }

  androidSwitchHandler = (val) => {
    this.setState({ isCanLoginAndroid: val === true ? 1 : 0 });
  }

  renderItem() {
    const {
      filterKeyword, jenisTerpilih, data, expandedRows, jarak, clicked,
    } = this.state;
    let i = 0;
    const itemRows = [];

    const arrayExpand = filterKeyword === '' || jenisTerpilih === 'All Data' ? expandedRows : [];
    const panjangArray = arrayExpand.length;
    const dataCarix = filterKeyword === '' ? data.filter(x => x.parent_id == null) : data;
    let dataCariy = dataCarix;
    if (jenisTerpilih === 'Display Menu') {
      dataCariy = data.filter(p => p.menu_is_display === '1');
    } else if (jenisTerpilih === 'Not Display As Menu') {
      dataCariy = data.filter(p => p.menu_is_display === '0');
    }
    let dataCari = [...dataCarix, ...dataCariy];
    /* remove duplicate */
    dataCari = _.uniqBy(dataCari, 'id');
    const arrayId = [];

    dataCari.forEach((dat) => {
      arrayId.push(dat.id);
      const kelas = clicked.indexOf(dat.id) !== -1 ? 'icon-chevron-up' : 'icon-chevron';
      const searchParent = data.filter(x => x.parent_id === dat.id);
      const isParent = searchParent.length > 0;
      itemRows.push(
        <tr onClick={() => this.handleRowClick(dat, 0)} key={`row-data-${dat.id}`}>
          <td>
            { isParent && filterKeyword === '' ? <i className={`table-icon ${kelas}`} /> : '' }
            {dat.name}
          </td>
          <td>
            {dat.menu_crontroller }
            /
            {dat.menu_function}
          </td>
          <td>
            <Switch
              className="text-right"
              checked={dat.id_m_cms_privilege != null}
              valueTrue="Yes"
              valueFalse="No"
              changeEvent={() => this.updateHakAkses(dat, 0)}
            />
          </td>
        </tr>,
      );
    });

    if (panjangArray > 0) {
      for (i = 0; i < panjangArray; i += 1) {
        const expanded = data.filter(x => x.parent_id === arrayExpand[i]);
        let j = 0;
        expanded.forEach((dat) => {
          const kelas = clicked.indexOf(dat.id) !== -1 ? 'icon-chevron-up' : 'icon-chevron';
          const indexArrayId = arrayId.indexOf(dat.parent_id);
          const searchParent = data.filter(x => x.parent_id === dat.id);
          const isParent = searchParent.length > 0;
          if (indexArrayId !== -1 && arrayId.indexOf(dat.id) === -1) {
            arrayId.splice(indexArrayId + 1 + j, 0, dat.id);
            const x = (
              <tr onClick={() => this.handleRowClick(dat, jarak[dat.parent_id])} key={`row-data-${dat.id}`}>
                <td>
                  <span style={{ paddingLeft: 15 * jarak[dat.parent_id] }}>
                    {
                      isParent
                      ? <i className={`table-icon ${kelas}`} />
                      : ''
                    }
                  {dat.name}
                  </span>
                </td>
                <td>
                  {dat.menu_crontroller }
                  /
                  {dat.menu_function}
                </td>
                <td>
                  <Switch
                    className="text-right"
                    checked={dat.id_m_cms_privilege != null}
                    valueTrue="Yes"
                    valueFalse="No"
                    changeEvent={() => this.updateHakAkses(dat, jarak[dat.parent_id])}
                  />
                </td>
              </tr>
            );
            itemRows.splice(indexArrayId + 1 + j, 0, x);
            j += 1;
          }
        });
      }
    }

    return itemRows;
  }

  render() {
    const {
      filterKeyword, jenisTerpilih, name, roleMenuOpt, landing, desc, isCanLoginAndroid,
    } = this.state;
    const allItemRows = this.renderItem();

    return (
      <div>
        <section className="panel">
          <div className="panel-heading">
            <h4 className="panel-title">Retina Role Detail</h4>
          </div>
          <div className="panel-heading table-header">
            <div className="row">
              <div className="col-md-12">
                <div style={{ width: '100%' }}>
                  <div className="row">
                    <div className="col-md-2" style={{ paddingRight: '0px', float: 'right' }}>
                      <InputText
                        classes="filter"
                        placeholder="Cari ..."
                        changeEvent={value => this.searchData(value)}
                        value={filterKeyword}
                      />
                    </div>
                    <div className="col-md-3" style={{ paddingRight: '0px', float: 'right' }}>
                      <Select
                        data={['All Data', 'Display Menu', 'Not Display As Menu']}
                        value={jenisTerpilih}
                        changeEvent={val => this.changeCustomJenis(val)}
                      />
                    </div>
                    {/* form above table */}
                    <div className="col-md-5" style={{ paddingRight: '0px', float: 'left' }}>
                      <div className="row">
                        <div className="col-md-6 mb-xs">
                          <InputText
                            label="Role Name"
                            placeholder="User Role Name"
                            changeEvent={value => this.setState({ name: value })}
                            value={name}
                          />
                        </div>
                        <div className="col-md-6 mb-xs">
                          <AutoComplete
                            label="Landing Page"
                            placeholder="Pilih landing page"
                            selector="name"
                            data={roleMenuOpt}
                            value={landing}
                            changeEvent={this.landingChangeHandler}
                            // TODO:
                          />
                        </div>
                        <div className="col-xs-12 mb-xs">
                          <Textarea
                            label="Description"
                            value={desc}
                            changeEvent={this.descChangeHandler}
                            // TODO:
                          />
                        </div>
                        <div className="col-md-6 mb-xs">
                          <div className="form-group">
                            <label htmlFor="" className="control-label mr-xs">
                              Is Can Login Android
                            </label>
                            <Switch
                              checked={parseInt(isCanLoginAndroid, 10) === 1}
                              // valueTrue="Yes"
                              // valueFalse="No"
                              changeEvent={this.androidSwitchHandler}
                              // TODO:
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    {/* end form above table */}
                    <div className="col-md-2" style={{ paddingRight: '0px', float: 'left' }}>
                      <Button
                        label="Update All"
                        clickEvent={() => this.updateAll()}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="panel-body">
            <table className="table interactive-table">
              <thead>
                <tr>
                  <th>Menu Name</th>
                  <th>Controller / Function</th>
                  <th>Is Accesable</th>
                </tr>
              </thead>
              <tbody>
                {allItemRows}
              </tbody>
            </table>
          </div>
        </section>
      </div>
    );
  }
}

export default CoreHOC(RetinaRoleDetail);

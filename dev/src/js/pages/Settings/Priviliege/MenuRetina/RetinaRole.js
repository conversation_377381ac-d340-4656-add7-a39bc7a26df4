import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { FieldFeedbacks, FieldFeedback } from 'react-form-with-constraints';

import CoreHOC from '../../../../core/CoreHOC';

import InputText from '../../../../components/form/InputText';
import SidePopup from '../../../../components/sidepopup/ContainerV2';
import Table from '../../../../components/table/v.2/Table';

import { getAllRoleCms, addCmsRole } from '../../../../data/setting/privilage';

import { catchError } from '../../../../utils/helper';
import { TABLE_META } from './config/RetinaRole';

class RetinaRole extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.node,
    router: PropTypes.shape({}),
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    notificationSystem: null,
    router: null,
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
  }

  constructor(props) {
    super(props);

    this.state = {
      data: [],
      name: '',
    };
  }

  componentWillMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([{ type: 'primary', content: <span> Add New Role </span>, action: () => { this.addRole(); } }]);
    this.getData();
  }

  getData() {
    const { notificationSystem } = this.props;
    getAllRoleCms().then((response) => {
      this.setState({
        data: response.data,
      });
    }).catch((err) => {
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(err),
        level: 'error',
      });
    });
  }

  callEditDetailHandler = (val) => {
    const { router } = this.props;
    router.push(`/privilage/retinarole/detail/${val.id}`);
  }

  addRole = () => {
    this.sidePop.showPopup();
  }

  saveHandler = () => {
    const { notificationSystem } = this.props;
    const { name } = this.state;
    const param = {
      name,
    };
    addCmsRole(param).then(() => {
      this.getData();
      this.sidePop.hidePopup();
    }).catch((err) => {
      notificationSystem.addNotification({
        title: 'Add data failed',
        message: catchError(err),
        level: 'error',
      });
      this.sidePop.failedCallback();
    });
  }

  handleChange = (val, type, e = null) => {
    this.setState({ [type]: val });
    if (e) {
      this.sidePop.validateInput(e.target);
    }
  }

  render() {
    const { data, name } = this.state;
    return (
      <div>
        <Table
            columns={TABLE_META}
            content={data}
            rowEvent={val => this.callEditDetailHandler(val._original)}
            withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
                <section className="panel">
                  <div className="panel-heading table-header">
                    <div className="row">
                      <div className="col-md-2">
                        <h4 className="panel-title" style={{ paddingTop: '8px' }}>
                          Retina Role
                        </h4>
                      </div>
                      <div className="col-md-10">
                        <div className="col-md-1" style={{ paddingLeft: '0px', float: 'right' }}>
                          <PageSize />
                        </div>
                        <div style={{ paddingRight: '20px', float: 'right' }}>
                          <InputSearch />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="panel-body">
                    {makeTable()}
                  </div>
                </section>
            )}
        />
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={520}
          saveHandle={() => this.saveHandler()}
          render={({ show }) => {
            if (show) {
              return (
                  <div>
                    <h4 className="side-popup-title">
                      Add User Role
                    </h4>
                    <div className="row mb-sm">
                      <div className="col-sm-12">
                        <InputText
                            label="Name"
                            placeholder="Name"
                            value={name}
                            changeEvent={(val, e) => this.handleChange(val, 'name', e)}
                            name="name_form"
                        />
                        <FieldFeedbacks for="name_form">
                          <FieldFeedback when={val => val === ''}>Name tidak boleh kosong</FieldFeedback>
                        </FieldFeedbacks>
                      </div>
                    </div>
                  </div>
              );
            }
            return (null);
          }}
        />
      </div>
    );
  }
}

export default CoreHOC(RetinaRole);

import React from 'react';
import { ReactTableDefaults } from 'react-table';

import Expander from '../../components/CellExpander';
import IndentLevel from '../../components/CellIndentLevel';
import IsDisplay from '../../components/CellIsDisplay';
import ControllerFunction from '../../components/CellControllerFunction';
import Detail from '../../components/CellDetail';

const buildColumns = openFormEventHandler => ([
    {
        ...ReactTableDefaults,
        width: 100,
        id: 'expander',
        expander: true,
        Expander,
    },
    {
        width: 300,
        Header: 'Name',
        accessor: 'name',
        Cell: IndentLevel,
    },
    {
        Header: 'Parent',
        accessor: 'parent_name',
    },
    {
        Header: 'Seq',
        accessor: 'urutan',
    },
    {
        Header: 'Is Display',
        accessor: 'menu_is_display',
        Cell: IsDisplay,
    },
    {
        Header: 'Controller/Function',
        accessor: 'menu_crontroller',
        Cell: ControllerFunction,
    },
    {
        Header: 'Content',
        accessor: 'content',
    },
    {
        Header: 'Detail',
        Cell: props => <Detail {...props} clickHandler={openFormEventHandler} />,
    },
]);

const FILTER_OPTIONS = [
    'All Data',
    'Display Menu',
    'Not Display As Menu',
];

export { buildColumns, FILTER_OPTIONS };

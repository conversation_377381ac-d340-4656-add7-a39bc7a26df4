import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

/* COMPONENTS */
import Select from '../../../components/form/Select';
import CoreHOC from '../../../core/CoreHOC';
import Table from '../../../components/table/v.2/Table';

/* DATA */
import { getBusinessLoan } from '../../../data/support';

/* CONFIG */
import { busninessMetaTabel, dataLimit } from './config/BusninessLoaning';

class BusinessLoaning extends Component {
  static propTypes = {
      calendar: PropTypes.shape({
        start: PropTypes.string,
        end: PropTypes.string,
      }),
      assignCalendar: PropTypes.func.isRequired,
      assignButtons: PropTypes.func.isRequired,
      router: PropTypes.shape({
        push: PropTypes.func,
      }).isRequired,
      notificationSystem: PropTypes.shape({
        addNotification: PropTypes.func,
      }),
      assignFilterColoumn: PropTypes.func,
      assignRangeDate: PropTypes.func,
      hideProgress: PropTypes.func.isRequired,
  }

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    notificationSystem: ({
      addNotification: () => {
        // do nothing
      },
    }),
    assignFilterColoumn: () => {
      // do nothing
    },
    assignRangeDate: () => {
      // do nothing
    },
  }

  constructor(props) {
    super(props);

    this.state = {
      calendar: {
        start: '',
        end: '',
      },
      resultPerpage: 10,
    };
  }

  componentWillMount() {
    const {
        assignFilterColoumn, assignRangeDate, assignButtons, assignCalendar, calendar, hideProgress,
    } = this.props;

    assignFilterColoumn([]);
    assignRangeDate([]);
    assignButtons([]);
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeCalendarHandler(startDate, endDate);
    }, true);
    const tanggalStart = calendar.start;
    const tanggalEnd = calendar.end;

    this.setState({
        calendar: {
          start: tanggalStart,
          end: tanggalEnd,
        },
    });
    hideProgress();
  }

  fetchDataHandler = async (state) => {
    const {
      calendar: { start, end }, resultPerpage,
    } = this.state;

    const {
      page, pages, sorted, filtered,
    } = state;

    let payload = {
      per_page: resultPerpage,
      page: parseInt(page, 10) + 1,
      start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
    };

    // get filter params
    if (filtered && filtered.length > 0) {
      const filterAll = filtered.find(data => data.id === 'all');
      payload = { ...payload, ...{ search: filterAll.value } };
    }

    // get sort params
    if (sorted.length > 0) {
      const { [sorted.length - 1]: { id, desc } } = sorted;
      payload = { ...payload, ...{ order: id, sort: desc ? 'DESC' : 'ASC' } };
    }

    let err = null,
    retval = { data: [], pageCount: pages > -1 ? pages : -1 };

    try {
      const resApi = await getBusinessLoan(payload);

      if (resApi) {
        const newData = resApi.data;
        const { total_page: totalPage } = resApi.metadata;
        retval = { data: newData, pageCount: parseInt(totalPage, 10) };
      }
    } catch (e) {
      err = e;
    }

    retval = { ...retval, ...{ err } };

    return retval;
  }

  callEditDetailHandler = ({ _original: data }) => {
    const { router } = this.props;
    router.push(`/business-dev/loan/detail/${data.no}`);
  }

  changeCalendarHandler = (start, end) => {
    const { assignCalendar } = this.props;
    assignCalendar(start, end);
    this.setState({
      calendar: {
        start,
        end,
      },
    }, () => {
      this.refetchList();
    });
  }

  ubahPage = (value) => {
    this.setState({
      resultPerpage: value,
    }, () => {
      this.table.forceRefetch();
    });
  }

  refetchList = () => {
    this.table.forceRefetch();
  }

  render() {
    const { resultPerpage } = this.state;

    return (
      <div>
        <Table
          ref={(c) => { this.table = c; }}
          columns={busninessMetaTabel}
          rowEvent={this.callEditDetailHandler}
          withWrapperRender={({
            makeTable, InputSearch,
          }) => (
              <section className="panel">
                <div className="panel-heading table-header">
                  <div className="row">
                    <div className="col-md-2">
                      <h4 className="panel-title" style={{ paddingTop: '8px' }}>Business Loan</h4>
                    </div>
                    <div className="col-md-10">
                      <div className="col-md-1" style={{ paddingLeft: '0px', float: 'right' }}>
                        <Select
                          data={dataLimit}
                          value={resultPerpage}
                          changeEvent={value => this.ubahPage(value)}
                          classes="displayCount mb-reset"
                        />
                      </div>
                      <div style={{ paddingRight: '10px', float: 'right' }}>
                        <InputSearch />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="panel-body">
                  {makeTable()}
                </div>
              </section>
            )}

          // server-side
          onFetch={this.fetchDataHandler}
        />
      </div>
    );
  }
}

export default CoreHOC(BusinessLoaning);

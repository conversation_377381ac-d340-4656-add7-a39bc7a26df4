import React, { Component } from 'react';
import update from 'immutability-helper';
import PropTypes from 'prop-types';
import CoreHOC from '../../../core/CoreHOC';

/* COMPONENTS */
import Table from '../../../components/table/v.2/Table';
import SwitchBox from '../../../components/form/SwitchBox';
import InputText from '../../../components/form/InputText';
import SidePopup from '../../../components/sidepopup/Container';
import InputTextArea from '../../../components/form/InputTextArea';

/* DATA */
import { getBusinessCoaching, updateBusinessCoaching } from '../../../data/support';

/* CONFIG */
import { TABLE_META, STATUS_FILTER_LIST, STATUS_EDIT_LIST } from './config/BusinessCoaching';

class BusinessCoaching extends Component {
  static propTypes = {
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    assignFilterColoumn: PropTypes.func,
    assignRangeDate: PropTypes.func,
  }

  static defaultProps = {
    assignCalendar: () => { },
    assignButtons: () => { },
    assignFilterColoumn: () => { },
    assignRangeDate: () => { },
  }

  constructor(props) {
    super(props);

    this.state = {
      switchFilter: 'all',
      dataList: [],
      dataListFiltered: [],
      detail: {
        id: '',
        status: '',
        detail: '',
        name: '',
        applicant: '',
        phone: '',
        email: '',
        address: '',
        province: '',
        city: '',
        goal: '',
        date: '',
        time: '',
      },
    };
  }

  componentWillMount() {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;
    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, null);
    assignButtons([]);
    this.getData();
  }

  getData() {
    getBusinessCoaching().then((res) => {
      this.setState({
        dataList: res.data,
        dataListFiltered: res.data,
      }, () => {
        const { switchFilter } = this.state;
        this.switchFilterHandler(switchFilter);
        this.sidePop.hidePopup();
      });
    });
  }

  callEditDetailHandler({ _original: data }) {
    const { detail } = this.state;
    const newDetail = update(detail, {
      id: { $set: data.id },
      status: { $set: data.status_meeting_txt },
      detail: { $set: data.tujuan_meeting },
      name: { $set: data.usaha },
      applicant: { $set: data.pemohon },
      phone: { $set: data.no_telp },
      email: { $set: data.alamat_email },
      address: { $set: data.alamat_lengkap },
      province: { $set: data.province },
      city: { $set: data.city },
      date: { $set: data.date },
      time: { $set: data.time },
    });
    this.setState({
      detail: newDetail,
    }, () => {
      this.sidePop.showPopup();
    });
  }

  statusChangeHandler(switchFilter) {
    const { detail } = this.state;
    const newDetail = update(detail, {
      status: { $set: switchFilter },
    });
    this.setState({
      detail: newDetail,
    });
  }

  saveHandle() {
    const { detail, dataListFiltered } = this.state;
    const cari = dataListFiltered.find(df => df.status_meeting_txt === detail.status && df.id === detail.id);
    if (cari !== undefined) {
      this.sidePop.hidePopup();
    } else {
      const param = {
        id: detail.id,
        status: detail.status,
      };
      updateBusinessCoaching(param).then(() => {
        this.getData();
      });
    }
  }

  switchFilterHandler(switchFilter) {
    const { dataList } = this.state;
    let dataListFiltered = [];

    if (switchFilter === 'all') {
      dataListFiltered = dataList;
    } else {
      dataListFiltered = dataList.filter(dl => dl.status_meeting_txt === switchFilter);
    }

    this.setState({
      switchFilter,
      dataListFiltered,
    });
  }

  render() {
    const {
      detail, switchFilter, dataListFiltered,
    } = this.state;
    return (
      <div>
        <Table
          ref={(c) => {
              this.table = c;
          }}
          columns={TABLE_META}
          content={dataListFiltered}
          rowEvent={val => this.callEditDetailHandler(val)}
          withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
              <section className="panel">
                  <div className="panel-heading table-header">
                      <div className="row">
                          <div className="col-md-3">
                              <h4 className="panel-title" style={{ margin: '8px' }}>
                                Business Coaching
                              </h4>
                          </div>
                          <div className="col-md-5">
                            <div style={{ textAlign: 'right' }}>
                              <SwitchBox
                                dataset={STATUS_FILTER_LIST}
                                value={switchFilter}
                                changeEvent={val => this.switchFilterHandler(val)}
                              />
                            </div>
                          </div>
                          <div className="col-md-3">
                            <InputSearch />
                          </div>
                          <div className="col-md-1">
                            <PageSize />
                          </div>
                      </div>
                  </div>
                  <div className="panel-body">
                    {makeTable()}
                  </div>
              </section>
          )}
        />
        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          saveHandle={() => this.saveHandle()}
        >
          <h4 className="side-popup-title">
            Business Coaching Detail
          </h4>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <div>
                <label className="control-label">Status</label>
              </div>
              <div>
                <SwitchBox
                  dataset={STATUS_EDIT_LIST}
                  value={detail.status}
                  changeEvent={val => this.statusChangeHandler(val)}
                />
              </div>
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputTextArea
                label="Detail"
                value={detail.detail}
                disabled
                row={2}
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText
                label="Business Name"
                value={detail.name}
                disabled
              />
            </div>
            <div className="col-sm-6">
              <InputText
                label="Applicant"
                value={detail.applicant}
                disabled
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText
                label="Phone"
                value={detail.phone}
                disabled
              />
            </div>
            <div className="col-sm-6">
              <InputText
                label="Email"
                value={detail.email}
                disabled
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-12">
              <InputTextArea
                label="Address"
                value={detail.address}
                disabled
                row={2}
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText
                label="Province"
                value={detail.province}
                disabled
              />
            </div>
            <div className="col-sm-6">
              <InputText
                label="City"
                value={detail.city}
                disabled
              />
            </div>
          </div>
          <div className="row mb-sm">
            <div className="col-sm-6">
              <InputText
                label="Date"
                value={detail.date}
                disabled
              />
            </div>
            <div className="col-sm-6">
              <InputText
                label="Time"
                value={detail.time}
                disabled
              />
            </div>
          </div>
        </SidePopup>
      </div>
    );
  }
}

export default CoreHOC(BusinessCoaching);

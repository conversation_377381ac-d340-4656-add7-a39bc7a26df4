import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { FieldFeedback, FieldFeedbacks } from 'react-form-with-constraints';

import { debounce } from 'lodash';
import CoreHOC from '../../../core/CoreHOC';
import { FORM_TYPES } from '../../../enum/form';
import {
    GROUP_FILTER_TYPE, GENERATE_ERROR_MESSAGE, STATUS_ID_VALUE,
} from './config/BackupDatabase';

import InputText from '../../../components/form/InputText';
import DateTimePicker from '../../../components/form/DateTimePicker';
import Autocomplete from '../../../components/form/Autocomplete';
import InputTextArea from '../../../components/form/InputTextArea';
import InputCheckboxDark from '../../../components/form/InputCheckboxDark';

class BackupMajooliteDetail extends PureComponent {
    changeEvent = async (key, val, e) => {
        const {
            changeEvent, changeUserPhoneHandler, validateInput, userList,
        } = this.props;

        await changeEvent(key, val);

        switch (key) {
            case 'user':
                await validateInput(this.hiddenEmail);
                await changeUserPhoneHandler(val, e, userList.find(x => x.id === val));
                break;
            default:
                await validateInput(e ? e.target : e);
                break;
        }
    }

    changeDateFilterHandler = async (val) => {
        const { changeEvent } = this.props;

        const tgl = moment(val).format('DD MMM YYYY HH:mm');
        await changeEvent('expiredTime', tgl);
    }

    getFileName = (val) => {
        const result = val.split('/');
        const filename = result.length > 1 ? result[result.length - 1] : '';

        return filename;
    }

    downloadDBFile = (fileUrl) => {
        if (fileUrl && fileUrl.length > 0) {
            window.location.href = fileUrl;
        }
    }

    render() {
        const {
            type,
            userList,
            data: {
                status_id: statusId, expiredTime,
                lastOrder, lastTrans, alamat, tlp, support, statusNew, statusDbUploaded,
                email, deviceSelected, logSyncUx, statusSolved, fileUrl,
                note, isAutomatic, emailOutlet,
            },
            eventScroll, showLogHandle, showLogActivity,
        } = this.props;

        const {
            last_log_sync: {
                createdate: lastDateSync, createby: lastSyncBy, status_id: lastSyncStatusId,
            }, log_sync: logSync,
        } = logSyncUx;

        return (
            <div>
                <h4 className="side-popup-title">
                    Backup/Sync Majoolite
                </h4>
                <div className="row mb-sm">
                    <div
                        className="col-sm-12"
                    >
                        <span className="control-label" htmlFor="time">Date Created</span>
                        <DateTimePicker
                            className="text-right date-time-picker-component"
                            value={expiredTime}
                            onChange={this.changeDateFilterHandler}
                            dateFormat="DD MMM YYYY"
                            timeFormat="HH:mm"
                        />
                    </div>
                </div>
                <div className="row mb-sm">
                    <div className="col-sm-12">
                        <label className="control-label">
                            User Phone Number
                            {' '}
                            <span className="text-red">
                                *
                            </span>
                        </label>
                        <Autocomplete
                            data={userList}
                            selector="name"
                            value={email}
                            changeEvent={debounce((val, e) => {
                                this.changeEvent('user', val, e);
                            }, 600)}
                            changeScroll={(val) => { eventScroll(val); }}
                            placeholder="Search ..."
                            name="user"
                            isPaginate
                            required
                        />
                        <div className="form-group hidden">
                            <input
                                ref={(c) => { this.hiddenEmail = c; }}
                                name="email_hidden"
                                type="text"
                                value={email}
                                required
                            />
                        </div>
                        <FieldFeedbacks for="email_hidden">
                            <FieldFeedback when="valueMissing">
                                {GENERATE_ERROR_MESSAGE('User Email', 'valueMissing')}
                            </FieldFeedback>
                        </FieldFeedbacks>
                    </div>
                </div>
                {
                    deviceSelected !== '' && isAutomatic && (
                        <div className="row mb-sm">
                            <div className="col-sm-6">
                                <InputText
                                    disabled
                                    label="Last Trx Order Number"
                                    value={lastOrder}
                                />
                            </div>
                            <div className="col-sm-6">
                                <InputText
                                    disabled
                                    label="Last Trx Payment Number"
                                    value={lastTrans}
                                />
                            </div>
                        </div>
                    )
                }
                <div className="divider mb-sm" style={{ borderColor: '#004a49' }} />
                <h6><strong style={{ color: '#00b7b5' }}>INFO OUTLET</strong></h6>
                <div className="row mb-sm">
                    <div className="col-sm-12">
                        <InputTextArea
                            disabled
                            label="Address"
                            value={alamat}
                        />
                    </div>
                </div>
                <div className="row mb-sm">
                    <div className="col-sm-6">
                        <InputText
                            disabled
                            label="Phone Number"
                            value={tlp}
                        />
                    </div>
                    <div className="col-sm-6">
                        <InputText
                            disabled
                            label="Email Outlet"
                            value={emailOutlet}
                        />
                    </div>
                </div>
                <div className="row mb-sm">
                    <div className="col-sm-12">
                        <InputText
                            disabled
                            label="Support"
                            value={support}
                        />
                    </div>
                </div>
                {
                    (
                        type !== FORM_TYPES.CREATE && GROUP_FILTER_TYPE.BACKUP.some(x => x.value === statusId)
                    )
                    && (
                        <div>
                            <div className="divider mb-sm" style={{ borderColor: '#004a49' }} />
                            <div className="row mb-sm">
                                <div className="col-sm-12">
                                    <label className="control-label" htmlFor="status"><p id="status">Status</p></label>
                                    <div className="stocklist-form form-stack">
                                        <div className="stack">
                                            <div className="inline-form form-group">
                                                <div className="form-wrap" style={{ width: '34%' }}>
                                                    <div className="form-group">
                                                        <div className="form-control checkbox-disabled-background">
                                                            <InputCheckboxDark
                                                                id="status-new"
                                                                name="status_new"
                                                                disabled
                                                                checked={statusNew}
                                                                changeEvent={val => this.changeEvent('statusNew', val, null)}
                                                            >
                                                                New
                                                            </InputCheckboxDark>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="form-wrap" style={{ width: '32%' }}>
                                                    <div className="form-group">
                                                        <div className="form-control checkbox-disabled-background">
                                                            <InputCheckboxDark
                                                                id="status-db-uploaded"
                                                                name="status_db_uploaded"
                                                                disabled
                                                                checked={statusDbUploaded}
                                                                changeEvent={val => this.changeEvent('statusDbUploaded', val, null)}
                                                            >
                                                                Db Uploaded
                                                            </InputCheckboxDark>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="form-wrap" style={{ width: '34%' }}>
                                                    <div className="form-group">
                                                        <div
                                                            className={`form-control ${statusId === STATUS_ID_VALUE.SOLVED && 'checkbox-disabled-background'}`}
                                                        >
                                                            <InputCheckboxDark
                                                                id="status-solved"
                                                                name="status_solved"
                                                                checked={statusSolved}
                                                                disabled={statusId === STATUS_ID_VALUE.SOLVED}
                                                                changeEvent={val => this.changeEvent('statusSolved', val, null)}
                                                            >
                                                                Solved
                                                            </InputCheckboxDark>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {
                                fileUrl.length > 0 && (
                                    <div className="row mb-sm">
                                        <div className="col-sm-12">
                                            <div className="backup-db-user-download">
                                                <div className="row">
                                                    <div className="col-sm-8">
                                                        <div className="help-download-icon" />
                                                        <span className="caption">
                                                            {this.getFileName(fileUrl)}
                                                        </span>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <button
                                                            onClick={() => { this.downloadDBFile(fileUrl); }}
                                                            type="button"
                                                            className="btn btn-primary btn-block"
                                                        >
                                                            Download File
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }
                        </div>
                    )
                }
                {
                    (
                        type !== FORM_TYPES.CREATE && [STATUS_ID_VALUE.SUCCESS_SYNC, STATUS_ID_VALUE.FAILED_SYNC].includes(statusId)
                    )
                    && (
                        <div>
                            <div className="divider mb-sm" style={{ borderColor: '#004a49' }} />
                            <div className="row mb-sm">
                                <div className="col-sm-8">
                                    <label className="control-label">
                                        {moment(lastDateSync, 'YYYY-MM-DD  HH:mm').format('DD MMM YYYY HH:mm:ss')}
                                        {' '}
                                        <b>
                                            {lastSyncBy}
                                            {' '}
                                            { }
                                            <font
                                                color={lastSyncStatusId === STATUS_ID_VALUE.FAILED_SYNC ? 'red' : 'white'}
                                            >
                                                {lastSyncStatusId === STATUS_ID_VALUE.FAILED_SYNC ? 'Failed' : 'Success'}
                                            </font>
                                        </b>
                                    </label>
                                </div>
                                <div className="col-sm-4 text-left">
                                    <span
                                        className={`log-activity-text ${showLogActivity ? 'active' : ''}`}
                                        onClick={() => showLogHandle(showLogActivity)}
                                        role="presentation"
                                    >
                                        {showLogActivity ? 'Hide Log Activity' : 'Show log Activity'}
                                    </span>
                                </div>
                            </div>
                            {
                                showLogActivity && (
                                    <div className="row mb-sm">
                                        <div className="col-sm-12">
                                            <div className="container-log">
                                                <div className="row">
                                                    {
                                                        logSync.map((x) => {
                                                            const {
                                                                id, status_id: statusIdLogs, createdate: createDateLog, createby: createByLog,
                                                            } = x;

                                                            return (
                                                                <div className="col-sm-12" key={id}>
                                                                    <label className="control-label">
                                                                        {moment(createDateLog, 'YYYY-MM-DD  HH:mm').format('DD MMM YYYY HH:mm:ss')}
                                                                        {' '}
                                                                        <b>
                                                                            {createByLog}
                                                                            {' '}
                                                                            { }
                                                                            <font
                                                                                color={Number(statusIdLogs) === Number(STATUS_ID_VALUE.FAILED_SYNC) ? 'red' : 'white'}
                                                                            >
                                                                                {Number(statusIdLogs) === Number(STATUS_ID_VALUE.FAILED_SYNC) ? 'Failed' : 'Success'}
                                                                            </font>
                                                                        </b>
                                                                    </label>
                                                                </div>
                                                            );
                                                        })
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                )
                            }
                            <div className="divider mb-sm" style={{ borderColor: '#004a49' }} />
                        </div>
                    )
                }
                <div className="row mb-sm">
                    <div className="col-sm-12">
                        <InputTextArea
                            label="note"
                            value={note}
                            changeEvent={(val, e) => this.changeEvent('note', val, e)}
                        />
                    </div>
                </div>
            </div>
        );
    }
}

BackupMajooliteDetail.propTypes = {
    type: PropTypes.string,
    data: PropTypes.shape({
        status_id: PropTypes.string,
        secretCode: PropTypes.string,
        expiredTime: PropTypes.string,
        lastOrder: PropTypes.string,
        lastTrans: PropTypes.string,
        alamat: PropTypes.string,
        tlp: PropTypes.string,
        support: PropTypes.string,
        statusNew: PropTypes.bool,
        statusDbUploaded: PropTypes.bool,
        email: PropTypes.string,
        outlet: PropTypes.string,
        deviceSelected: PropTypes.string,
        emailOutlet: PropTypes.string,
        logSyncUx: PropTypes.shape({
            last_log_sync: PropTypes.shape({
                createdate: PropTypes.string,
                createby: PropTypes.string,
                status_id: PropTypes.string,
            }),
            log_sync: PropTypes.arrayOf(
                PropTypes.shape({
                    id: PropTypes.string,
                    status_id: PropTypes.string,
                    createdate: PropTypes.string,
                    createby: PropTypes.string,
                }),
            ),
        }),
        statusSolved: PropTypes.bool,
        fileUrl: PropTypes.string,
        note: PropTypes.string,
        isAutomatic: PropTypes.bool,
        source: PropTypes.string,
    }),
    userList: PropTypes.arrayOf(
        PropTypes.shape({}),
    ),
    showLogActivity: PropTypes.bool,
    changeEvent: PropTypes.func.isRequired,
    changeUserPhoneHandler: PropTypes.func.isRequired,
    eventScroll: PropTypes.func.isRequired,
    showLogHandle: PropTypes.func.isRequired,
    validateInput: PropTypes.func.isRequired,
};

BackupMajooliteDetail.defaultProps = {
    type: FORM_TYPES.CREATE,
    data: {
        status_id: '',
        secretCode: '',
        expiredTime: '',
        lastOrder: '',
        lastTrans: '',
        alamat: '',
        tlp: '',
        support: '',
        fileUrl: '',
        note: '',
        email: '',
        outlet: '',
        deviceSelected: '',
        emailOutlet: '',
        statusNew: false,
        statusDbUploaded: false,
        statusSolved: false,
        isAutomatic: false,
        logSyncUx: PropTypes.shape({
            last_log_sync: PropTypes.shape({
                createdate: '',
                createby: '',
                status_id: '',
            }),
            log_sync: PropTypes.arrayOf(
                PropTypes.shape({
                    id: '',
                    status_id: '',
                    createdate: '',
                    createby: '',
                }),
            ),
        }),
    },
    userList: [],
    showLogActivity: false,
};

export default CoreHOC(BackupMajooliteDetail);

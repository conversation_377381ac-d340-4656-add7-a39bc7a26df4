import React, { Component } from 'react';
import { connect } from 'react-redux';

import update from 'immutability-helper';
import moment from 'moment';
import PropTypes from 'prop-types';
import CoreHOC from '../../../core/CoreHOC';

/* COMPONENTS */
import Table from '../../../components/table/v.2/Table';
import Select from '../../../components/form/Select';
import SidePopup from '../../../components/sidepopup/ContainerV2';

/* DATA */
import {
  getBackupDatabase, updateBackupDatabase, createBackupDatabase, syncDatabase, logUxSync,
} from '../../../data/support';

import {
  getUserActiveNew, listCabang, getDevice, getDeviceLastTrx,
} from '../../../data';

/* CONFIG */
import {
  SYNC_TYPE, TABLE_META, STATUS_FILTER_LIST, STATUS_ID_VALUE, GROUP_FILTER_TYPE,
  SOURCE_FILTER_LIST,
  BACKUP_SOURCE,
} from './config/BackupDatabase';

import { catchError } from '../../../utils/helper';
import { FORM_TYPES } from '../../../enum/form';

import './style.less';
import BackupDashboardDetail from './BackupDashboardDetail';
import BackupMajooliteDetail from './BackupMajooliteDetail';
import { getBunakenUser } from '../../../data/setting/kloposUser';

@connect(state => ({
  idUser: state.user.profile.id,
}))
class BackupDatabase extends Component {
  constructor(props) {
    super(props);
    this.state = {
      type: FORM_TYPES.CREATE,
      userDbList: [],
      userEmailList: [],
      userList: [],
      branchList: [],
      deviceList: [],
      switchFilter: '',
      sourceFilter: '',
      dropDownDisabled: false,
      showLogActivity: false,
      detail: {
        id: '',
        username: '',
        fileUrl: '',
        email: '',
        outlet: '',
        support: '',
        status_id: '',
        secretCode: '',
        note: '',
        lastOrder: '',
        lastTrans: '',
        alamat: '',
        tlp: '',
        emailOutlet: '',
        deviceSelected: '',
        isAutomatic: true,
        statusNew: false,
        statusDbUploaded: false,
        statusSolved: false,
        expiredTime: moment().format('DD MMM YYYY HH:mm'),
        logSyncUx: {
          last_log_sync: {
            createdate: '',
            createby: '',
            status_id: '',
          },
          log_sync: [],
        },
        source: BACKUP_SOURCE.DASHBOARD,
      },
      userListFetchState: {
        page: 1,
        keyword: '',
      },
    };
  }

  componentDidMount = () => {
    const {
      assignFilterColoumn, assignRangeDate, assignCalendar, assignButtons,
    } = this.props;

    assignFilterColoumn([]);
    assignRangeDate([]);
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([
      {
        type: 'dropdown',
        content: (
          <span>
            <i className="fa fa-plus" />
            Create New Request
          </span>
        ),
        listButton: [
          {
            text: 'Dashboard',
            func: () => this.handleNewBackupDatabase(BACKUP_SOURCE.DASHBOARD),
          },
          {
            text: 'Majoolite',
            func: () => this.handleNewBackupDatabase(BACKUP_SOURCE.MAJOO_LITE),
          },
        ],
      },
    ]);

    this.loadData();
  }

  changeDateHandler = (startDate, endDate) => {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);

    this.loadData();
  }

  fetchUserByEmail = async () => {
    const { notificationSystem } = this.props;
    const {
      userListFetchState: {
        page, keyword,
      },
    } = this.state;
    const param = {
      resultPerpage: 30, page, keyword,
    };

    try {
      const { data } = await getUserActiveNew(param);

      const userEmailList = data.map(x => ({ id: x.id, name: x.name }));

      this.setState({ userEmailList });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed get User List Data',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  fetchBunakenUser = async () => {
    const { notificationSystem } = this.props;
    const {
      userListFetchState: {
        page, keyword,
      },
    } = this.state;
    const param = {
      limit: 30, page, search: keyword,
    };

    try {
      const { data } = await getBunakenUser(param);

      const userList = data.map(x => ({ ...x, id: String(x.user_id), name: `${x.merchant_name} (${x.phone_number})` }));

      this.setState({ userList });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed get User List Data',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  getSecretCode = () => {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 4; i += 1) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }

    return text;
  }

  handleNewBackupDatabase = (source) => {
    this.setState({
      type: FORM_TYPES.CREATE,
      branchList: [],
      deviceList: [],
      detail: {
        id: '',
        username: '',
        fileUrl: '',
        email: '',
        outlet: '',
        support: '',
        status_id: '',
        secretCode: this.getSecretCode(),
        note: '',
        lastOrder: '',
        lastTrans: '',
        alamat: '',
        tlp: '',
        emailOutlet: '',
        deviceSelected: '',
        isAutomatic: true,
        statusNew: false,
        statusDbUploaded: false,
        statusSolved: false,
        expiredTime: moment().add(96, 'hours').format('DD MMM YYYY HH:mm'),
        logSyncUx: {
          last_log_sync: {
            createdate: '',
            createby: '',
            status_id: '',
          },
          log_sync: [],
        },
        source,
      },
      dropDownDisabled: false,
    }, () => {
      this.openSidePopup();
    });
  }

  openSidePopup = () => {
    const { detail } = this.state;
    if (detail.source !== BACKUP_SOURCE.MAJOO_LITE) {
      this.fetchUserByEmail();
    } else {
      this.fetchBunakenUser();
    }
    this.sidePop.showPopup();
  }

  callEditDetailHandler = async ({ _original: data }) => {
    const { showProgress, hideProgress, notificationSystem } = this.props;

    const {
      id_user: idUser, id_cabang: idCabang,
    } = data;

    showProgress();
    try {
      await this.fetchDevice(idCabang);
      await this.fetchCabang(idUser);
      await this.setDetailData(data);
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed get Data',
        message: catchError(err),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  setDetailData = async (data) => {
    const { detail, branchList } = this.state;

    const {
      id, id_user: idUser, user,
      id_cabang: idCabang, support, device_id: idDevice,
      file_url: fileUrl, secret_code: secretCode,
      status_id: statusId, note, exp_date: expDate, source,
    } = data;

    const userRes = await getBunakenUser({
      limit: 30, page: 1, id: idUser,
    });

    const branch = branchList.find(x => x.id === idCabang);

    let alamat = '';
    let tlp = '';
    let emailOutlet = '';
    let userList = [];

    if (branch) {
      const { alamat: alamatBranch, tlp: tlpBranch, email: emailBranch } = branch;
      alamat = alamatBranch;
      tlp = tlpBranch;
      emailOutlet = emailBranch;
    }

    if (userRes && userRes.data && userRes.data.length > 0) {
      const foundData = userRes.data[0];
      const { phone_number: phoneNumber, email } = foundData;
      userList = [{
        ...foundData,
        id: String(idUser),
        name: `${userRes.data[0].merchant_name} (${phoneNumber})`,
      }];
      tlp = phoneNumber;
      emailOutlet = email;
    }

    const newDetail = update(detail, {
      id: { $set: id },
      username: { $set: idUser },
      email: { $set: idUser },
      outlet: { $set: idCabang },
      support: { $set: support },
      status_id: { $set: statusId },
      secretCode: { $set: secretCode || '' },
      note: { $set: note },
      deviceSelected: { $set: idDevice },
      fileUrl: { $set: fileUrl },
      alamat: { $set: alamat },
      tlp: { $set: tlp },
      emailOutlet: { $set: emailOutlet },
      isAutomatic: { $set: true },
      statusNew: { $set: statusId === STATUS_ID_VALUE.NEW },
      statusDbUploaded: { $set: statusId === STATUS_ID_VALUE.DB_UPLOADER },
      statusSolved: { $set: statusId === STATUS_ID_VALUE.SOLVED },
      expiredTime: { $set: moment(expDate).format('DD MMM YYYY HH:mm') },
      source: { $set: source },
    });

    this.setState({
      type: FORM_TYPES.EDIT,
      detail: newDetail,
      userEmailList: [{ id: idUser, name: user }],
      userList,
    }, async () => {
      if (GROUP_FILTER_TYPE.SYNC.some(x => x.value === statusId)) {
        await this.logUxSync({ idCabang, idUser, idDevice });
      }
      this.openSidePopup();
    });
  }

  logUxSync = async ({ idCabang, idUser, idDevice }) => {
    const { notificationSystem } = this.props;
    const { detail } = this.state;
    const { logSyncUx } = detail;

    try {
      const { data } = await logUxSync({
        cabang_id: idCabang, user_id: idUser, device_id: idDevice,
      });

      const newDetail = update(detail, {
        logSyncUx: { $set: data.length !== 0 ? data : logSyncUx },
      });

      this.setState({
        detail: newDetail,
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed get Log Data',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  saveHandler = async (saveType, errorCallback) => {
    const { showProgress, hideProgress } = this.props;
    const { detail, type } = this.state;
    const {
      id, statusSolved, note, secretCode, outlet, username,
      expiredTime, deviceSelected, source,
    } = detail;

    const formatedExpiredTime = moment(expiredTime, 'DD MMM YYYY HH:mm').format('YYYY-MM-DD HH:mm:ss');

    let payload = {
      code: secretCode,
      exp: formatedExpiredTime,
      cabang: outlet,
      user: username,
      device: deviceSelected,
      note,
      source,
    };

    showProgress();
    try {
      if (type === FORM_TYPES.CREATE) {
        await this.createBackupHandle(payload);
      } else {
        payload = {
          id,
          status: statusSolved ? STATUS_ID_VALUE.SOLVED : detail.status_id,
          ...payload,
        };

        await this.updateBackupDatabase(payload);
      }

      await this.loadData();
    } catch (err) {
      errorCallback();
    } finally {
      hideProgress();
    }
  }

  createBackupHandle = async (payload) => {
    const { notificationSystem } = this.props;

    try {
      await createBackupDatabase(payload);
      this.sidePop.hidePopup();
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed Create Data',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  updateBackupDatabase = async (payload) => {
    const { notificationSystem } = this.props;

    try {
      await updateBackupDatabase(payload);
      this.sidePop.hidePopup();
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed Edit Data',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  changeUserEmailHandler = (val, e) => {
    const { detail } = this.state;

    if (val === '') {
      this.cariKeyword(e);
    }

    const newValue = update(detail, {
      username: { $set: val },
      outlet: { $set: '' },
      support: { $set: '' },
      deviceSelected: { $set: '' },
      alamat: { $set: '' },
      tlp: { $set: '' },
      emailOutlet: { $set: '' },
    });

    this.setState({
      detail: newValue,
    }, () => {
      if (val !== '') {
        this.fetchCabang(val);
      }
    });
  }

  changeUserPhoneHandler = (val, e, selectedUser) => {
    const { detail } = this.state;

    if (val === '') {
      this.cariKeyword(e);
    }

    const newValue = update(detail, {
      email: { $set: selectedUser ? selectedUser.user_id : '' },
      username: { $set: selectedUser ? selectedUser.user_id : '' },
      outlet: { $set: selectedUser ? selectedUser.outlet_id : '' },
      support: { $set: '' },
      deviceSelected: { $set: '' },
      alamat: { $set: '' },
      tlp: { $set: selectedUser ? selectedUser.phone_number : '' },
      emailOutlet: { $set: selectedUser ? selectedUser.email : '' },
    });

    this.setState({
      detail: newValue,
    });
  }

  fetchDevice = async (idOutlet) => {
    const { notificationSystem } = this.props;

    try {
      const {
        data: deviceList, status, msg,
      } = await getDevice({ id_outlet: idOutlet });
      if (!status) throw new Error(msg);

      this.setState({
        deviceList,
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Get Devices data failed',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  fetchCabang = async (user) => {
    const { notificationSystem } = this.props;

    try {
      const { data: branchList } = await listCabang({ user });

      this.setState({
        branchList,
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed get Cabang List',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  changeOutletHandler = async (val) => {
    const { detail, branchList } = this.state;
    const { isAutomatic } = detail;

    let newSupport = '';
    const branch = branchList.find(bl => bl.id === val);

    if (branch && branch.support) {
      newSupport = branch.support.length > 0 ? branch.support : 'REGULER';
    }

    const newDetail = await update(detail, {
      support: { $set: newSupport },
      alamat: { $set: branch ? branch.alamat : '' },
      tlp: { $set: branch ? branch.tlp : '' },
      emailOutlet: { $set: branch ? branch.email : '' },
      deviceSelected: { $set: '' },
    });

    this.setState({
      detail: newDetail,
    }, async () => {
      if (isAutomatic) {
        this.fetchDevice(val);
      }
    });
  }

  changeAutomaticHadler = (val) => {
    const { detail } = this.state;

    const newValue = update(detail, {
      isAutomatic: { $set: val },
    });

    this.setState({
      detail: newValue,
      dropDownDisabled: !val,
    });
  }

  loadData = async () => {
    const {
      showProgress, hideProgress,
      calendar: { start, end }, notificationSystem,
    } = this.props;

    const payload = {
      start: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
      end: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
    };

    showProgress();
    try {
      const { data } = await getBackupDatabase(payload);

      const userDbList = data.map(x => Object.assign({}, x, {
        support: x.support.trim().length > 0 ? x.support : 'REGULER',
      }));

      this.setState({ userDbList });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed to Get Data',
        message: catchError(err),
        level: 'error',
      });
    } finally {
      hideProgress();
    }
  }

  cariKeyword = (value) => {
    const { detail } = this.state;
    this.setState({
      userListFetchState: {
        keyword: value,
        page: 1,
      },
    }, () => {
      if (value.length >= 3 || value === '') {
        if (detail.source !== BACKUP_SOURCE.MAJOO_LITE) {
          this.fetchUserByEmail();
        } else {
          this.fetchBunakenUser();
        }
      }
    });
  }

  eventScroll = (val) => {
    const { userListFetchState, detail } = this.state;

    const newuserListFetchState = update(userListFetchState, {
      page: { $set: val + 1 },
    });
    this.setState({
      userListFetchState: newuserListFetchState,
    }, () => {
      if (detail.source !== BACKUP_SOURCE.MAJOO_LITE) {
        this.fetchUserByEmail();
      } else {
        this.fetchBunakenUser();
      }
    });
  }

  changeDevice = async (val) => {
    const { detail } = this.state;
    const { outlet } = detail;

    const newValue = update(detail, {
      deviceSelected: { $set: val },
    });

    this.setState({
      detail: newValue,
    }, () => {
      this.fetchDevicesLastTrx(val, outlet);
    });
  }

  fetchDevicesLastTrx = async (device, idOutlet) => {
    const { notificationSystem } = this.props;
    const { detail } = this.state;

    try {
      const { data: { order, transaction } } = await getDeviceLastTrx({ device, id_outlet: idOutlet });

      const newDetail = update(detail, {
        lastOrder: { $set: order },
        lastTrans: { $set: transaction },
      });

      this.setState({
        detail: newDetail,
      });
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed get Devices Last Transaction',
        message: catchError(err),
        level: 'error',
      });
    }
  }

  syncHandle = async (val) => {
    const {
      showProgress, hideProgress, notificationSystem, idUser,
    } = this.props;
    const { detail } = this.state;
    const {
      username, outlet, note, deviceSelected,
    } = detail;

    try {
      showProgress();
      this.disabledDropDownButtonHandle(true);

      const payload = {
        user_id: username,
        cabang_id: outlet,
        device_id: deviceSelected,
        sync_type: val,
        note,
        createby: idUser,
      };

      const { message } = await syncDatabase(payload);

      notificationSystem.addNotification({
        title: 'Success Sync Data',
        message,
        level: 'success',
      });
      this.sidePop.hidePopup();
      this.loadData();
    } catch (err) {
      notificationSystem.addNotification({
        title: 'Failed Sync Data',
        message: catchError(err),
        level: 'error',
      });
    } finally {
      this.disabledDropDownButtonHandle(false);
      hideProgress();
    }
  }

  disabledDropDownButtonHandle = (dropDownDisabled) => {
    this.setState({
      dropDownDisabled,
    });
  }

  filteredData = (switchFilter, sourceFilter) => {
    const { userDbList } = this.state;

    return userDbList.filter(x => (
      (switchFilter === '' || x.status_id === switchFilter)
      && (sourceFilter === '' || x.source === sourceFilter)
    ));
  }

  showLogHandle = (val) => {
    this.setState({
      showLogActivity: !val,
    });
  }

  changeEvent = async (key, val) => {
    const { detail } = this.state;

    const newDetail = await update(detail, {
      [key]: {
        $set: val,
      },
    });

    this.setState({
      detail: newDetail,
    });
  }

  render() {
    const {
      userEmailList, userList, branchList, detail, type, switchFilter,
      deviceList, dropDownDisabled, showLogActivity, sourceFilter,
    } = this.state;

    return (
      <div>
        <Table
          ref={(c) => { this.table = c; }}
          columns={TABLE_META}
          content={this.filteredData(switchFilter, sourceFilter)}
          rowEvent={val => this.callEditDetailHandler(val)}
          withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
            <section className="panel">
              <div className="panel-heading table-header">
                <div className="row">
                  <div className="col-md-4">
                    <h4 className="panel-title">
                      Backup/Sync
                    </h4>
                  </div>
                  <div className="col-md-2">
                    <Select
                      data={STATUS_FILTER_LIST}
                      value={switchFilter}
                      changeEvent={(val) => { this.setState({ switchFilter: val }); }}
                    />
                  </div>
                  <div className="col-md-3">
                    <InputSearch />
                  </div>
                  <div className="col-md-2">
                    <Select
                      data={SOURCE_FILTER_LIST}
                      value={sourceFilter}
                      changeEvent={(val) => { this.setState({ sourceFilter: val }); }}
                    />
                  </div>
                  <div className="col-md-1">
                    <PageSize />
                  </div>
                </div>
              </div>
              <div className="panel-body">
                {makeTable()}
              </div>
            </section>
          )}
        />

        <SidePopup
          ref={(c) => { this.sidePop = c; }}
          width={560}
          type={type}
          saveHandle={this.saveHandler}
          dropDownButton={
            type === FORM_TYPES.CREATE ? (
              {
                buttonDropDownText: 'Sinkronisasi',
                buttonDropDownDisabled: dropDownDisabled,
                buttonDropDownListButton: [
                  {
                    text: 'Hard Sinkronisasi',
                    func: () => this.syncHandle(SYNC_TYPE.HARD),
                  },
                  {
                    text: 'Sinkronisasi',
                    func: () => this.syncHandle(SYNC_TYPE.NORMAL),
                  },
                ],
              }
            ) : undefined
          }
          render={({ validateInput }) => (detail.source !== BACKUP_SOURCE.MAJOO_LITE ? (
            <BackupDashboardDetail
              type={type}
              data={detail}
              userEmailList={userEmailList}
              branchList={branchList}
              deviceList={deviceList}
              showLogActivity={showLogActivity}
              changeAutomaticHadler={this.changeAutomaticHadler}
              changeUserEmailHandler={this.changeUserEmailHandler}
              eventScroll={this.eventScroll}
              changeOutletHandler={this.changeOutletHandler}
              changeDevice={this.changeDevice}
              showLogHandle={this.showLogHandle}
              changeEvent={this.changeEvent}
              validateInput={validateInput}
            />
          ) : (
            <BackupMajooliteDetail
              type={type}
              data={detail}
              userList={userList}
              showLogActivity={showLogActivity}
              changeUserPhoneHandler={this.changeUserPhoneHandler}
              eventScroll={this.eventScroll}
              showLogHandle={this.showLogHandle}
              changeEvent={this.changeEvent}
              validateInput={validateInput}
            />
          ))}
        />
      </div>
    );
  }
}

BackupDatabase.propTypes = {
  assignCalendar: PropTypes.func,
  assignButtons: PropTypes.func,
  notificationSystem: PropTypes.shape({
    addNotification: PropTypes.func,
  }),
  calendar: PropTypes.shape({
    start: PropTypes.string,
    end: PropTypes.string,
  }),
  idUser: PropTypes.string,
  assignFilterColoumn: PropTypes.func,
  assignRangeDate: PropTypes.func,
  showProgress: PropTypes.func.isRequired,
  hideProgress: PropTypes.func.isRequired,
};

BackupDatabase.defaultProps = {
  calendar: {
    start: '',
    end: '',
  },
  assignCalendar: () => { },
  assignButtons: () => { },
  notificationSystem: ({
    addNotification: () => { },
  }),
  idUser: '',
  assignFilterColoumn: () => { },
  assignRangeDate: () => { },
};

export default CoreHOC(BackupDatabase);

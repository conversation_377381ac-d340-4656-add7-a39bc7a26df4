import DateTimeColumn from '../../../../components/table/components/DateTimeColumn';
import NullableColumn from '../../../../components/table/components/NullableColumn';

const TABLE_META = [
    {
        Header: 'Source',
        accessor: 'source_name',
        Cell: ({ value }) => (value === 'Premium' ? 'Dashboard' : value || '-'),
    },
    {
        Header: 'Request Time',
        accessor: 'request_date',
        Cell: DateTimeColumn,
    },
    {
        Header: 'User',
        accessor: 'user',
        Cell: NullableColumn,
    },
    {
        Header: 'Outlet',
        accessor: 'outlet',
        Cell: NullableColumn,
    },
    {
        Header: 'Support',
        accessor: 'support',
        Cell: NullableColumn,
    },
    {
        Header: 'OS',
        accessor: 'os',
        Cell: NullableColumn,
    },
    {
        Header: 'Versi APK',
        accessor: 'apk',
        Cell: NullableColumn,
    },
    {
        Header: 'ID Device',
        accessor: 'device_id',
        Cell: NullableColumn,
    },
    {
        Header: 'Status',
        accessor: 'status',
        Cell: NullableColumn,
    },
    {
        Header: 'Updated On',
        accessor: 'request_date',
        Cell: DateTimeColumn,
    },
];

const SYNC_TYPE = {
    NORMAL: 1,
    HARD: 2,
};

const STATUS_ID_VALUE = {
    NEW: '39',
    DB_UPLOADER: '5',
    SOLVED: '21',
    WAITING_SYNC: '29',
    IN_PROGRESS_SYNC: '28',
    FAILED_SYNC: '27',
    PAUSED_SYNC: '26',
    SUCCESS_SYNC: '25',
};

const STATUS_FILTER_LIST = [
    {
        value: '',
        text: 'All Status',
        type: 'backup',
    },
    {
        value: '39',
        text: 'New',
        type: 'backup',
    },
    {
        value: '5',
        text: 'DB Uploaded',
        type: 'backup',
    },
    {
        value: '21',
        text: 'Solved',
        type: 'backup',
    },
    {
        value: '29',
        text: 'Waiting Sync',
        type: 'sync',
    },
    {
        value: '28',
        text: 'Sync In Progress',
        type: 'sync',
    },
    {
        value: '27',
        text: 'Sync Failed',
        type: 'sync',
    },
    {
        value: '26',
        text: 'Sync Paused',
        type: 'sync',
    },
    {
        value: '25',
        text: 'Synced',
        type: 'sync',
    },
];

const BACKUP_SOURCE = {
    DASHBOARD: '0',
    MAJOO_LITE: '1',
};

const SOURCE_FILTER_LIST = [
    {
        value: '',
        text: 'All Source',
    },
    {
        value: BACKUP_SOURCE.DASHBOARD,
        text: 'Dashboard',
    },
    {
        value: BACKUP_SOURCE.MAJOO_LITE,
        text: 'Majoolite',
    },
];

const GROUP_FILTER_TYPE = {
    BACKUP: STATUS_FILTER_LIST.filter(x => x.type === 'backup'),
    SYNC: STATUS_FILTER_LIST.filter(x => x.type === 'sync'),
};

const GENERATE_ERROR_MESSAGE = (key, type) => {
    const errorType = [
        {
            type: 'valueMissing',
            text: `Please fill ${key}`,
        },
        {
            type: 'wrongEmailFormat',
            text: 'Incorrect Email Format',
        },
    ];

    const errorCode = errorType.find(x => x.type === type);
    return errorCode.text;
};

export {
    TABLE_META, SYNC_TYPE, STATUS_FILTER_LIST, STATUS_ID_VALUE, GROUP_FILTER_TYPE, GENERATE_ERROR_MESSAGE, BACKUP_SOURCE, SOURCE_FILTER_LIST,
};

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import CoreHOC from '../../../core/CoreHOC';
import { catchError, hoursToHumanize } from '../../../utils/helper';

/* COMPONENTS */
import Table, { getFilterValue, getUpdatedFilterValue } from '../../../components/table/v.2/Table';

/* DATA */
import { getAccessLogin } from '../../../data/support';

/* CONFIG */
import { TABLE_META, FILTER_TYPE } from './config/AccessLogin';

class AccessLogin extends PureComponent {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    assignFilterColoumn: PropTypes.func,
    assignButtons: PropTypes.func,
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
  }

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    assignCalendar: () => {},
    assignFilterColoumn: () => {},
    assignButtons: () => { },
    notificationSystem: {
      addNotification: null,
    },
  }

  constructor(props) {
    super(props);
    const { calendar } = this.props;

    this.state = {
      filterTable: [
        { id: FILTER_TYPE.DATE_RANGE, value: calendar },
      ],
    };
  }

  componentDidMount() {
    const {
      assignCalendar, assignButtons, assignFilterColoumn,
    } = this.props;

    assignFilterColoumn([]);
    assignButtons([]);
    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
  }

  updateCustomFilter = (val, type, callback = () => {}) => {
    const { filterTable } = this.state;
    const updatedFilterValue = getUpdatedFilterValue(filterTable, type, val);
    this.setState({ filterTable: updatedFilterValue }, () => callback);
  }

  _onFetch = async (state) => {
    const { notificationSystem } = this.props;
    const {
      page, pages, sorted, filtered, pageSize,
    } = state;
    const { length } = sorted;

    const { start, end } = getFilterValue(filtered, FILTER_TYPE.DATE_RANGE);

    let payload = {
        start_date: moment(start, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        end_date: moment(end, 'DD/MM/YYYY').format('YYYY-MM-DD'),
        per_page: pageSize,
        page: Number(page) + 1,
    };

    // get sort params
    if (length > 0) {
        const { [length - 1]: { id, desc } } = sorted;
        payload = { ...payload, ...{ order: id, sort: desc ? 'DESC' : 'ASC' } };
    }

    // get filter params
    if (filtered && filtered.length > 0) {
        const filterAll = filtered.find(data => data.id === 'all');
        if (filterAll && filterAll.value !== '') {
            payload = {
                ...payload,
                search: filterAll.value,
            };
        }
    }

    let err = null,
        retval = {
            data: [],
            pageCount: pages > -1 ? pages : -1,
        };

    try {
        const res = await getAccessLogin(payload);
        const { data, meta: { last_page: pageCount } } = res;

        const result = data.map(item => ({
          date: item.start_date,
          username: item.username,
          outlet: item.outlet_name ? item.outlet_name : '-',
          merchant: item.store_name ? item.store_name : '-',
          password: item.code,
          status: item.STATUS,
          note: item.note,
          duration: hoursToHumanize(item.duration),
          copy: {
            text: `user: ${item.username} / pass: ${item.code}`,
            status: item.STATUS,
            message: 'Username and password copied to clipboard.',
          },
        }));

        retval = { data: result, pageCount };
    } catch (e) {
        err = e;
        notificationSystem.addNotification({
            title: 'Gagal mendapatkan data',
            message: catchError(err),
            level: 'error',
        });
    }
    retval = { ...retval, ...{ err } };
    return retval;
  }

  changeDateHandler(start, end) {
    const { assignCalendar } = this.props;
    assignCalendar(start, end);

    const calendar = { start, end };
    this.updateCustomFilter(calendar, 'dateRange');
  }


  render() {
    const { filterTable } = this.state;

    return (
      <Table
        ref={(c) => {
            this.table = c;
        }}
        columns={TABLE_META}
        filters={filterTable}
        withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
          <section className="panel">
              <div className="panel-heading table-header">
                  <div className="row">
                      <div className="col-md-8">
                          <h4 className="panel-title" style={{ margin: '8px' }}>
                            Support Access Login
                          </h4>
                      </div>
                      <div className="col-md-3">
                        <InputSearch />
                      </div>
                      <div className="col-md-1">
                        <PageSize />
                      </div>
                  </div>
              </div>
              <div className="panel-body">
                {makeTable()}
              </div>
          </section>
        )}
          // server-side
          onFetch={this._onFetch}
      />
    );
  }
}

export default CoreHOC(AccessLogin);

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';

import CoreHOC from '../../../../core/CoreHOC';
import Table, { getFilterValue } from '../../../../components/table/v.2/Table';
import Select from '../../../../components/form/Select';

import {
  getSubmission, printExcel,
} from '../../../../data/setting/marketplace';

import { catchError } from '../../../../utils/helper';
import { convertDate, style } from './helper';

import { tableMeta, statusList } from './config'

@CoreHOC
export default class Tokopedia extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }).isRequired,
    showProgress: PropTypes.func.isRequired,
    hideProgress: PropTypes.func.isRequired,
  };

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    notificationSystem: {
      addNotification: null,
    },
    router: {
      push: null,
    },
    assignCalendar: () => { },
    assignButtons: () => {},
  };

  constructor(props) {
    super(props);

    this.state = {
        statusTerpilih: '',
        tanggalStart: '',
        tanggalEnd: '',
    };
  }

  componentWillMount() {
    const {
      assignButtons, assignCalendar, calendar,
    } = this.props;

    assignCalendar(null, null, (startDate, endDate) => {
      this.changeDateHandler(startDate, endDate);
    });
    assignButtons([
      {
        type: 'primary',
        content: (
          <span>
            <i className="fa fa-download" />
            Download Pengajuan
          </span>
        ),
        action: () => this.handleDownloadPengajuan(),
      },
    ]);

    const tanggalStart = calendar.start;
    const tanggalEnd = calendar.end;

    this.setState({ tanggalStart, tanggalEnd });
  }

  componentDidUpdate(prevProps, prevState) {
    const { statusTerpilih } = this.state;

    if (prevState.statusTerpilih !== statusTerpilih) {
      this.table.forceRefetch();
    }
  }

  fetchDataHandler = async ({ page, pages, filtered, pageSize, sorted }) => {
    const { showProgress, hideProgress, notificationSystem } = this.props;
    const { tanggalStart, tanggalEnd, statusTerpilih } = this.state;

    const filterKeyword = getFilterValue(filtered, 'all');

    let payload = {
      page: page + 1,
      limit: pageSize,
      ...filterKeyword && { filterKeyword }, // {search: filterKeyword}
      start_date: moment(tanggalStart, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      end_date: moment(tanggalEnd, 'DD-MM-YYYY').format('YYYY-MM-DD'),
      status: statusTerpilih,
      type: '1',
    };

    // get filter params
    if (filtered && filtered.length > 0) {
      const filterAll = filtered.find(data => data.id === 'all');
      payload = { ...payload, ...{ search: filterAll.value } };
    }

    if (sorted.length > 0) {
      const { [sorted.length - 1]: { id, desc } } = sorted;

      Object.assign(payload, {
          column: id,
          isAsc: desc ? 'DESC' : 'ASC',
      });
    }

    const err = null;
    let retval = { data: [], pageCount: pages > -1 ? pages : -1 };

    showProgress();

    try {
      const res = await getSubmission(payload);

      if (!res) throw new Error('Gagal Mendapatkan Data');
      retval = { data: res.data, pageCount: res.total_page };
    } catch (e) {
      notificationSystem.addNotification({
        title: 'Gagal Mendapatkan Data Akun',
        message: catchError(e),
        level: 'error',
      });
    }

    hideProgress();

    retval = Object.assign({}, retval, {
      err,
    });

    return retval;
  }

  handleDownloadPengajuan = async () => {
    const {
      notificationSystem, hideProgress, showProgress,
    } = this.props;
    const submissionList = this.table.getterFakeContent();

    showProgress();

    const param = {
      data: submissionList,
      type: '1',
    };

    try {
      const res = await printExcel(param);
      if (!res.status) throw Error('Gagal mendapatkan data');
      window.location = res.data;
    } catch (err) {
        notificationSystem.addNotification({
          title: 'Terjadi Kesalahan',
          message: catchError(err),
          level: 'error',
        });
    }

    hideProgress();
  }

  callEditDetailHandler = ({ _original: data}) => {
    const { router } = this.props;

    router.push(`/marketplace/tokopedia/detail/${data.submission_number}`);
  }

  changeCustomStatus(statusTerpilih) {
    this.setState({ statusTerpilih });
  }

  changeDateHandler(startDate, endDate) {
    const { assignCalendar } = this.props;
    assignCalendar(startDate, endDate);
    this.setState({
        tanggalStart: startDate,
        tanggalEnd: endDate,
    }, () => {
      this.table.forceRefetch();
    });
  }

  render() {
    const { calendar } = this.props;
    const { statusTerpilih } = this.state;

    return (
        <Table
            ref={(c) => {
                this.table = c;
            }}
            columns={tableMeta}
            rowEvent={val => this.callEditDetailHandler(val)}
            withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
                <section className="panel">
                    <div className="panel-heading">
                      <h4 className="panel-title mt-reset">
                        Tokopedia Integration
                      </h4>
                      <h5
                        className="subtitle"
                        style={style.subtitleStyle}
                      >
                        {`${convertDate(calendar.start)} - ${convertDate(calendar.end)}`}
                      </h5>
                    </div>
                    <div className="panel-heading table-header">
                      <div className="row">
                        <div className="col-md-12">
                          <div style={{ width: '100%' }}>
                            <div className="row">
                              <div className="col-md-1" style={{ paddingLeft: '0px', float: 'right' }}>
                                <PageSize />
                              </div>
                              <div className="col-md-4" style={{ paddingLeft: '0px', float: 'right' }}>
                                <InputSearch />
                              </div>
                              <div className="col-md-2" style={{ paddingLeft: '0px', float: 'right' }}>
                                <Select
                                  data={statusList}
                                  value={statusTerpilih}
                                  changeEvent={val => this.changeCustomStatus(val)}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="panel-body">
                        {makeTable()}
                    </div>
                </section>
            )}

            // server-side
            onFetch={this.fetchDataHandler}
        />
    );
  }
}

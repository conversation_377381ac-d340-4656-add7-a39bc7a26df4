import React from 'react';

import NumberDateTimeColumn from '../../../../components/table/components/NumberDateTimeColumn';
import MarketplaceStatus from '../components/MarketplaceStatusTokped';

const tableMeta = [
    {
        Header: 'Merchant Name',
        accessor: 'merchant.name',
        minWidth: 175,
    },
    {
        Header: 'Email',
        accessor: 'merchant.email',
        minWidth: 175,
    },
    {
        Header: 'Owner Name',
        accessor: 'merchant.owner_name',
        minWidth: 160,
    },
    {
        Header: 'Status',
        accessor: 'outlets',
        minWidth: 610,
        Cell: ({ original }) => <MarketplaceStatus data={original.outlets} />,
    },
    {
        Header: 'Created Date',
        accessor: 'createdate',
        Cell: NumberDateTimeColumn,
        minWidth: 150,
    },
    {
        Header: 'Updated Date',
        accessor: 'updatedate',
        minWidth: 150,
        Cell: (row) => {
            const { original: { log_update_name: logName} } = row;
            return (
              <div style={{ display: 'flex', flexDirection: 'column' }}>
                <NumberDateTimeColumn {...row} />
                {logName && (
                  <div>{`By ${logName}`}</div>
                )}
              </div>
            )
        }
    },
];

const statusList = [
    {
        value: '',
        option: 'All',
    },
    {
        value: '0',
        option: 'Unpaired',
    },
    {
        value: '1',
        option: 'In progress',
    },
    {
        value: '2',
        option: 'Rejected',
    },
    {
        value: '4',
        option: 'Approved',
    },
    {
        value: '5',
        option: 'Unpair Request',
    },
    {
        value: '6',
        option: 'Integrated',
    },
];

export { tableMeta, statusList };

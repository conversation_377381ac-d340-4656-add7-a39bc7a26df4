import React, { Component } from 'react';
import PropTypes from 'prop-types';
import update from 'immutability-helper';
import CoreHOC from '../../../../core/CoreHOC';
import { getDetailSubmissionV12, saveSubmission } from '../../../../data/setting/marketplace';
import { PROVIDER_ID } from '../../../../data/setting/marketplace/enum';
import { catchError } from '../../../../utils/helper';
import { STATUS_INTEGRATION, STATUS_LIST } from '../GrabFood/config';
import MainForm from '../components/MainForm';
import GrabForm from '../components/GrabForm';
import ModalPopup from '../../../../components/modalpopup/Container';

@CoreHOC
export default class GrabmartDetail extends Component {
    initialButtonActions = [
        {
            id: '1',
            type: null,
            content: (
                <span>
                  Batal
                </span>
            ),
            action: () => this.gotoBasePath(),
            isDisabled: false,
        },
        {
            id: '2',
            type: 'primary',
            content: (
                <span>
                  Save
                </span>
            ),
            action: () => this.saveHandle(),
            isDisabled: true,
        },
    ];

    static propTypes = {
        params: PropTypes.shape({
            id: PropTypes.string,
        }),
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        location: PropTypes.shape({
            pathname: PropTypes.string,
        }).isRequired,
    };

    static defaultProps = {
        params: {
            id: '',
        },
        notificationSystem: {
            addNotification: () => {},
        },
        router: {
            push: null,
        },
        assignCalendar: () => { },
        assignButtons: () => {},
    };

    constructor(props) {
        super(props);

        this.state = {
            submission: {
                name: '',
                pemilikUsaha: '',
                jumlahOutlet: 0,
                outlets: {},
                sendOutlet: [],
            },
            innerWidthScreen: window.innerWidth,
            footerButtons: this.initialButtonActions,
            isAnyChange: false,
            statusChange: '',
        };
    }

    componentDidMount() {
        const {
            assignButtons, assignCalendar,
        } = this.props;

        assignCalendar(null, null, null);
        assignButtons([]);

        this.getData();
    }

    getSubmission = async () => {
        const {
            params: { id }, notificationSystem,
        } = this.props;

        try {
            const res = await getDetailSubmissionV12(id, { provider_id: PROVIDER_ID.GRABMART_INTEGRATION });
            if (!res.status) throw Error('Gagal mendapatkan data');
            const { data } = res;
            const { outlets, merchant } = data;
            Object.assign(outlets, { status_awal: outlets.status });

            const submission = {
                name: merchant.name,
                pemilikUsaha: merchant.owner_name,
                emailAddress: merchant.email,
                submissionId: data.submission_id,
                submissionNumber: data.submission_number,
                providerId: data.provider_id,
                createdBy: data.createby,
                outlets,
            };
            this.setState({
                submission,
            });
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        }
    }

    getData = async () => {
        const { hideProgress } = this.props;

        await this.getSubmission();

        hideProgress();
    }

    changeButtonAction = (value) => {
        const footerButtons = update(this.initialButtonActions, {
            0: { isDisabled: { $set: false } },
            1: { isDisabled: { $set: value } },
        });

        this.setState({ footerButtons });
    }

    gotoBasePath = () => {
        const { router } = this.props;

        router.push('/marketplace/grabmart/');
    }

    saveHandle = async () => {
        const form = this.form.getStateForm();
        const { submission: { outlets } } = this.state;
        const conditionStatus = form.status !== outlets.status;
        const conditionDesc = form.desc !== outlets.description && form.desc !== '' && form.status === STATUS_INTEGRATION.REJECTED;
        const conditionMID = form.mid !== outlets.mid && form.mid !== '' && form.status === STATUS_INTEGRATION.INTEGRATED;
        if (conditionStatus || conditionDesc || conditionMID) {
            const statusChange = STATUS_LIST.find(({ value }) => value === form.status);
            let Status = '';
            if (statusChange) {
                Status = statusChange.option;
            }
            this.setState({ isAnyChange: true, statusChange: Status }, () => {
                this.addNewActivity.showPopup();
            });
            return;
        }
        this.addNewActivity.showPopup();
    }

    saveExceptionHandler = async () => {
        const { submission, isAnyChange } = this.state;
        const { outlets } = submission;
        if (!isAnyChange) {
            this.addNewActivity.hidePopup();
            return;
        }
        const {
            showProgress, hideProgress, notificationSystem, router,
        } = this.props;

        const form = this.form.getStateForm();

        const payload = {
            id_user: submission.createdBy,
            submission_id: submission.submissionId,
            submission_number: submission.submissionNumber,
            provider_id: parseInt(submission.providerId, 10),
            merchant: {
                name: submission.name,
                email: submission.emailAddress,
                owner_name: submission.pemilikUsaha,
            },
            outlets: [{
                ...outlets,
                status: form.status,
                mid: form.mid,
                description: form.desc !== '' ? form.desc : outlets.description,
                employee_name: form.employee,
            }],
        };

        showProgress();
        try {
            const res = await saveSubmission(payload);
            if (!res.status) throw Error('Gagal Mengubah Data');
            notificationSystem.addNotification({
                title: 'Berhasil Melakukan approval Submission',
                level: 'success',
            });

            setTimeout(() => {
                router.push('/marketplace/grabmart/');
            }, 1000);
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        }
        hideProgress();

        this.addNewActivity.hidePopup();
    }

    render() {
        const {
            submission,
            footerButtons,
            innerWidthScreen,
            isAnyChange,
            statusChange,
        } = this.state;
        const { outlets } = submission;

        const fixedFooterWidth = (innerWidthScreen - (265));

        return (
            <div className="row">
                <div className="col-sm-12">
                    <div className="panel">
                        <div className="panel-heading">
                            <h4 className="title">Grabmart Integration</h4>
                        </div>
                        <div className="panel-body">
                            <MainForm
                                data={submission}
                                ref={(c) => { this.form = c; }}
                                changeButtonSave={this.changeButtonAction}
                                grabType={PROVIDER_ID.GRABMART_INTEGRATION}
                            />
                            <div className="border-component-sidepopup" />
                            <GrabForm
                                data={submission.outlets}
                                grabType={PROVIDER_ID.GRABMART_INTEGRATION}
                            />
                        </div>
                    </div>
                </div>
                <div className="fixed-button-wrapper-bottom" style={{ width: fixedFooterWidth, zIndex: 1009 }}>
                    <div className="row mb-lg">
                        <div className="col-sm-12 text-right">
                            {footerButtons.map(x => (<button key={x.id} type="button" className={`btn ${x.type ? `btn-${x.type}` : ''}`} onClick={!x.isDisabled ? x.action : () => { }} disabled={x.isDisabled}>{x.content}</button>))}
                        </div>
                    </div>
                </div>
                <ModalPopup
                    title="Confirmation Grabmart Integration"
                    confirmHandle={() => { this.saveExceptionHandler(); }}
                    confirmText={isAnyChange ? 'Simpan' : 'Ok'}
                    ref={(c) => { this.addNewActivity = c; }}
                >
                    <div className="row mb-sm">
                        <div className="col-sm-12">
                            {
                                isAnyChange
                                    ? (
                                        <div>
                                            Apakah anda yakin ingin mengirim outlet yang di ubah dengan status :
                                            <ul>
                                                <li>
                                                    <b>
                                                        {outlets.id_outlet}
                                                    </b>
                                                    {' '}
                                                    -
                                                    {' '}
                                                    {outlets.name}
                                                    {' '}
                                                    :
                                                    {' '}
                                                    <b>
                                                        {statusChange}
                                                    </b>
                                                </li>
                                            </ul>
                                        </div>
                                    ) : (
                                        <div>
                                            Anda Belum melakukan beberapa perubahan pada status outlet
                                        </div>
                                    )
                            }
                        </div>
                    </div>
                </ModalPopup>
            </div>
        );
    }
}

import React, { Component } from 'react';
import moment from 'moment';
import PropTypes from 'prop-types';
import CoreHOC from '../../../../core/CoreHOC';
import { getFilterValue, getUpdatedFilterValue } from '../../../../utils/table.util';
import { PROVIDER_ID } from '../../../../data/setting/marketplace/enum';
import { getSubmissionV12, printExcel } from '../../../../data/setting/marketplace';
import Table from '../../../../components/table/v.2/Table';
import { COLUMN_CONFIG, STATUS_LIST } from '../GrabFood/config';
import Select from '../../../../components/form/Select';
import { catchError } from '../../../../utils/helper';
import { printProviderName } from '../utils';

@CoreHOC
export default class Grabmart extends Component {
    static propTypes = {
        calendar: PropTypes.shape({
            start: PropTypes.string,
            end: PropTypes.string,
        }),
        notificationSystem: PropTypes.shape({
            addNotification: PropTypes.func,
        }),
        router: PropTypes.shape({
            push: PropTypes.func,
        }),
        assignCalendar: PropTypes.func,
        assignButtons: PropTypes.func,
        location: PropTypes.shape({
            pathname: PropTypes.string,
        }).isRequired,
        showProgress: PropTypes.func.isRequired,
        hideProgress: PropTypes.func.isRequired,
    };

    static defaultProps = {
        calendar: {
            start: '',
            end: '',
        },
        notificationSystem: {
            addNotification: null,
        },
        router: {
            push: null,
        },
        assignCalendar: () => { },
        assignButtons: () => {},
    };

    constructor(props) {
        super(props);
        const { calendar: { start, end } } = props;
        this.state = {
            submissionList: [],
            filters: [
                { id: 'date', value: { start, end } },
                { id: 'status', value: '' },
                { id: 'all', value: '' },
            ],
        };
    }

    componentDidMount() {
        const {
            assignButtons, assignCalendar, calendar,
        } = this.props;

        assignCalendar(null, null, (start, end) => {
            this.updateFilter({ start, end }, 'date');
        });
        assignButtons([
            {
                type: 'primary',
                content: (
                    <span>
                      <i className="fa fa-download" />
                      Download Pengajuan
                    </span>
                ),
                action: () => this.handleDownloadPengajuan(),
            },
        ]);

        this.updateFilter({ start: calendar.start, end: calendar.end }, 'date');
    }

    updateFilter = (val, type) => {
        const { filters } = this.state;
        const updatedFilterValue = getUpdatedFilterValue(filters, type, val);

        this.setState({ filters: updatedFilterValue, [type]: val });
    }

    convertDate = date => moment(date, 'DD-MM-YYYY').format('YYYY-MM-DD');

    fetchingData = async (state) => {
        const { hideProgress, showProgress } = this.props;
        const {
            pageSize, page, pages, filtered,
        } = state;

        const filterDate = getFilterValue(filtered, 'date');
        const filterKeyword = getFilterValue(filtered, 'all');
        const filterStatus = getFilterValue(filtered, 'status');

        const payload = {
            limit: pageSize,
            page: page + 1,
            provider_id: PROVIDER_ID.GRABMART_INTEGRATION,
            search: filterKeyword,
            status: filterStatus,
            start_date: this.convertDate(filterDate.start),
            end_date: this.convertDate(filterDate.end),
        };

        let err = null,
            resData = [],
            pageCount = pages > -1 ? pages : -1;
        try {
            showProgress();
            const res = await getSubmissionV12(payload);
            resData = res.data;
            pageCount = res.meta.last_page;
        } catch (e) {
            err = e;
        }
        hideProgress();
        this.setState({ submissionList: resData });
        return { data: resData, pageCount: parseInt(pageCount, 10), err };
    }

    handleDownloadPengajuan = async () => {
        const {
            notificationSystem, hideProgress, showProgress,
        } = this.props;
        const {
            submissionList,
        } = this.state;

        showProgress();

        const param = {
            data: submissionList,
        };

        try {
            const res = await printExcel(param);
            if (!res.status) throw Error('Gagal mendapatkan data');
            window.location = res.data;
        } catch (err) {
            notificationSystem.addNotification({
                title: 'Terjadi Kesalahan',
                message: catchError(err),
                level: 'error',
            });
        }

        hideProgress();
    }

    callEditDetailHandler = ({ _original }) => {
        const { router } = this.props;
        const { submission_no: id } = _original;

        router.push(`/marketplace/grabmart/detail/${id}`);
    }

    render() {
        const { filters } = this.state;
        return (
            <div>
                <Table
                    columns={COLUMN_CONFIG}
                    filters={filters}
                    rowEvent={this.callEditDetailHandler}
                    withWrapperRender={({ makeTable, InputSearch, PageSize }) => (
                        <section className="panel">
                            <div className="panel-heading table-header">
                                <div className="row">
                                    <div className="col-md-12">
                                        <div style={{ width: '100%' }}>
                                            <div className="row">
                                                <div className="col-md-5 pt-xs">
                                                    <h4 className="panel-title">{printProviderName(PROVIDER_ID.GRABMART_INTEGRATION)}</h4>
                                                </div>
                                                <div className="col-md-1" style={{ paddingLeft: '0px', float: 'right' }}>
                                                    <PageSize />
                                                </div>

                                                <div className="col-md-4" style={{ paddingLeft: '0px', float: 'right' }}>
                                                    <InputSearch />
                                                </div>
                                                <div className="col-md-2" style={{ paddingLeft: '0px', float: 'right' }}>
                                                    <Select
                                                        data={STATUS_LIST}
                                                        value={filters.find(val => val.id === 'status').value}
                                                        changeEvent={val => this.updateFilter(val, 'status')}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="panel-body">
                                {makeTable()}
                            </div>
                        </section>
                    )}
                    onFetch={this.fetchingData}
                />
            </div>
        );
    }
}

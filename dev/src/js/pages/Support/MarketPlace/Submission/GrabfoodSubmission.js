import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
import moment from 'moment';

import Table, { getFilterValue, getUpdatedFilterValue } from '../../../../components/table/v.2/Table';
import Select from '../../../../components/form/Select';

import CoreHOC from '../../../../core/CoreHOC';
import { catchError } from '../../../../utils/helper';

import * as walletApi from '../../../../data/wallet';

import { tableMeta, statusFilterList, FILTER_TYPES } from './config/grabConfig';
import { SUB_TYPE_SUBMISSIONS } from '../../../../enum/submissionType';
import { PROVIDER_ID } from '../../../../data/setting/marketplace/enum';


class GrabFoodSubmission extends Component {
  static propTypes = {
    calendar: PropTypes.shape({
      start: PropTypes.string,
      end: PropTypes.string,
    }),
    assignCalendar: PropTypes.func,
    assignButtons: PropTypes.func,
    router: PropTypes.shape({
      push: PropTypes.func,
    }),
    notificationSystem: PropTypes.shape({
      addNotification: PropTypes.func,
    }),
  }

  static defaultProps = {
    calendar: {
      start: '',
      end: '',
    },
    assignCalendar: null,
    assignButtons: null,
    router: {
      push: null,
    },
    notificationSystem: ({
      addNotification: () => {
        // do nothing
      },
    }),
  }

  constructor(props) {
    super(props);
    const date = props.calendar;
    this.state = {
      filterTable: [
        { id: FILTER_TYPES.FILTER_DATE, value: { start: date.start, end: date.end } },
        { id: FILTER_TYPES.FILTER_STATUS, value: '0' },
      ],
    };
  }

  componentWillMount() {
    const {
      assignCalendar, assignButtons,
    } = this.props;
    assignCalendar(null, null, (startDate, endDate) => {
      this._updateFilters(FILTER_TYPES.FILTER_DATE, { start: startDate, end: endDate });
    });
    assignButtons();
  }

  callEditDetailHandler = ({ _original: data }) => {
    const { router } = this.props;
    router.push(`/marketplace/grabfoodSubmission/detail-grabfood/${data.submission_no}`);
  }

  reformateDate = val => moment(val, 'DD-MM-YYYY').format('YYYY-MM-DD');

  _updateFilters = (type, value) => {
    const { assignCalendar } = this.props;
    const { filterTable } = this.state;

    const updatedFilterValue = getUpdatedFilterValue(filterTable, type, value);

    if (type === FILTER_TYPES.FILTER_DATE) {
      const { start: startDate, end: endDate } = value;
        assignCalendar(startDate, endDate);
    }

    this.setState({ filterTable: updatedFilterValue });
  }


  fetchDataHandler = async (state) => {
    const { notificationSystem } = this.props;

    const {
      page, pages, sorted, filtered, pageSize,
    } = state;

    let payload = {
      page: parseInt(page, 10) + 1,
      limit: pageSize,
      type: SUB_TYPE_SUBMISSIONS.GRAB,
      provider_id: PROVIDER_ID.GRABFOOD_SUBMISSION,
    };

    // get sort params
    if (sorted.length > 0) {
      const { [sorted.length - 1]: { id, desc } } = sorted;
      payload = { ...payload, ...{ order: id, sort: desc ? 'DESC' : 'ASC' } };
    }

    // get filter params
    if (filtered && filtered.length > 0) {
      const filterAll = getFilterValue(filtered, 'all');
      if (filterAll) {
        payload = { ...payload, search: filterAll };
      }

      const filterDate = getFilterValue(filtered, 'filterDate');
      if (filterDate) {
        payload = {
          ...payload,
          start_date: this.reformateDate(filterDate.start),
          end_date: this.reformateDate(filterDate.end),
        };
      }

      const filterStatus = getFilterValue(filtered, 'filterStatus');
      if (filterStatus && filterStatus !== statusFilterList[0].id) {
        payload = {
          ...payload,
          status: filterStatus,
        };
      }
    }

    let err = null,
    retval = { data: [], pageCount: pages > -1 ? pages : -1 };

    try {
      const res = await walletApi.getListSubmission(payload);

      const { last_page: totalPage } = res.meta;
      retval = { data: res.data, pageCount: parseInt(totalPage, 10) };
    } catch (e) {
      err = e;
      notificationSystem.addNotification({
        title: 'Gagal mendapatkan data',
        message: catchError(err),
        level: 'error',
      });
    }
    retval = { ...retval, ...{ err } };

    return retval;
  }

  render() {
    const { filterTable } = this.state;
    const statusFilter = filterTable.find(data => data.id === 'filterStatus').value;
    return (
      <div>
        <Table
          ref={(c) => { this.table = c; }}
          columns={tableMeta}
          rowEvent={this.callEditDetailHandler}
          filters={filterTable}
          withWrapperRender={({
            makeTable, InputSearch, PageSize,
          }) => (
            <section className="panel">
              <div className="panel-heading table-header">
                <div className="row">
                  <div className="col-md-5">
                    <h4 className="title">Grab Food Submission</h4>
                  </div>
                  <div className="col-md-4">
                    <Select
                      data={statusFilterList}
                      value={statusFilter}
                      changeEvent={val => this._updateFilters(FILTER_TYPES.FILTER_STATUS, val)}
                    />
                  </div>
                  <div className="col-md-2">
                    <InputSearch />
                  </div>
                  <div className="col-md-1">
                    <PageSize />
                  </div>
                </div>
                <div className="panel-body">
                  {makeTable()}
                </div>
              </div>
            </section>
          )}

          // server-side
          onFetch={this.fetchDataHandler}
        />
      </div>
    );
  }
}

export default CoreHOC(GrabFoodSubmission);

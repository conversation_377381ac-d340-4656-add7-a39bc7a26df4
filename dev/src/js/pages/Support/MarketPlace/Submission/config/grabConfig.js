import StatusApproval from '../../../../../components/table/components/StatusApproval';
import NumberDateTimeColumn from '../../../../../components/table/components/NumberDateTimeColumn';

const BUSNINESS_TYPE = {
    PERSEORANGAN: '1',
    BADAN_HUKUM: '2',
    KEMITRAAN: '3',
    PERUSAHAAN: '4',
    PEMERINTAN: '5',
    LAINNYA: '6',
};

const STATUS_SUBMISSION = {
    IN_PROGRESS: '1',
    REJECTED: '2',
    APPROVED: '4',
    SUSPEND: '9',
};

const tableMeta = [
    {
      Header: 'Busniness Name',
      accessor: 'merchant_name',
    },
    {
      Header: 'Provider',
      accessor: 'wallet_name',
    },
    {
      Header: 'Outlet',
      accessor: 'outlet_name',
    },
    {
      Header: 'Status',
      accessor: 'status',
      titleStyles: { width: '15%' },
      Cell: StatusApproval,
    },
    {
      Header: 'Create Date',
      accessor: 'createdate',
      Cell: NumberDateTimeColumn,
    },
    {
      Header: 'Update Date',
      accessor: 'updatedate',
      Cell: NumberDateTimeColumn,
    },
    {
      Header: 'Keterangan',
      accessor: 'notes',
    },
];

const statusFilterList = [
    {
      id: '0',
      name: 'Semua Data',
    },
    {
      id: '1',
      name: 'In Progress',
    },
    {
      id: '2',
      name: 'Reject',
    },
    {
      id: '4',
      name: 'Approve',
    },
    {
      id: '9',
      name: 'Suspend',
    },
];

const FILTER_TYPES = {
    FILTER_DATE: 'filterDate',
    FILTER_STATUS: 'filterStatus',
};

export {
    BUSNINESS_TYPE, tableMeta, statusFilterList, STATUS_SUBMISSION, FILTER_TYPES,
};

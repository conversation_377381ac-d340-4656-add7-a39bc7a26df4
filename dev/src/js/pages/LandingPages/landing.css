.landing {
  text-align: center;
}
.landing .landing-icon {
  margin-top: 60px;
}
.landing .landing-background-1 {
  position: absolute;
  width: 890px;
  height: 890px;
  left: 1072px;
  top: -330px;
  /* Turq/300 */
  background: #00B7B5;
  opacity: 0.6;
  filter: blur(400px);
}
.landing .landing-background-2 {
  position: sticky;
  width: 435px;
  height: 435px;
  left: -131px;
  top: 768px;
  /* Turq/200 */
  background: #66D4D3;
  opacity: 0.7;
  filter: blur(350px);
}
.landing .landing-header {
  font-weight: 600;
  font-size: 22px;
  line-height: 32px;
  color: #FFFFFF;
  margin-bottom: 22px;
}
.landing .landing-body {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 10px;
}
.landing .landing-body .landing-menu {
  display: grid;
  justify-content: center;
  align-content: center;
  gap: 15px;
  grid-auto-flow: column;
  color: #ffffff;
}
.landing .landing-body .landing-menu .landing-menu-icon {
  border: 1px solid #004A49;
  border-radius: 6px;
  height: 125px;
  width: 125px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  flex: auto;
  cursor: pointer;
  z-index: 1;
}
.landing .landing-body .landing-menu .landing-menu-icon img {
  margin-bottom: 10px;
}
.landing .landing-body .landing-menu :hover {
  background: #005252;
}
.landing .landing-body .landing-menu a:hover {
  color: #ffffff;
  text-decoration: none;
}
